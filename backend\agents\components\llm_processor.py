"""
LLM processor component for the Datagenius agent system.

This module provides a component for processing messages using LLMs.
"""

import logging
import os
import sys
from typing import Dict, Any, List, Optional
from pathlib import Path

# Add backend root to path for import_utils
backend_root = Path(__file__).parent.parent.parent
if str(backend_root) not in sys.path:
    sys.path.insert(0, str(backend_root))

# Import AgentProcessingContext using centralized import
from app.utils.import_utils import import_agent_schemas
AgentProcessingContext, _ = import_agent_schemas()

from .base import AgentComponent
from ..utils.prompt_template import PromptTemplate

logger = logging.getLogger(__name__)

# Import LLM providers conditionally to avoid hard dependencies
try:
    from langchain_groq import ChatGroq
    GROQ_AVAILABLE = True
except ImportError:
    GROQ_AVAILABLE = False
    logger.warning("Groq integration not available. Install with 'pip install langchain-groq'")

# Lazy import to avoid memory issues during startup
OPENAI_AVAILABLE = True
try:
    import langchain_openai
except ImportError:
    OPENAI_AVAILABLE = False
    logger.warning("OpenAI integration not available. Install with 'pip install langchain-openai'")


class LLMProcessorComponent(AgentComponent):
    """Component for processing messages using LLMs."""

    def __init__(self):
        """Initialize the LLM processor component."""
        super().__init__()
        self.llm = None
        self.prompt_templates = {}
        self.default_prompt_name = "default"

    async def _initialize(self, config: Dict[str, Any]) -> None:
        """
        Initialize the component with configuration.

        Args:
            config: Configuration dictionary for the component
        """
        # Initialize LLM based on provider
        provider = config.get("provider", "openai").lower()

        if provider == "groq":
            if not GROQ_AVAILABLE:
                raise ImportError("Groq integration not available. Install with 'pip install langchain-groq'")

            api_key = config.get("api_key", os.getenv("GROQ_API_KEY", ""))
            model_name = config.get("model", "llama3-70b-8192")
            temperature = config.get("temperature", 0.7)

            try:
                # Try with the newer parameter name (groq_api_key)
                self.llm = ChatGroq(
                    temperature=temperature,
                    model_name=model_name,
                    groq_api_key=api_key
                )
            except TypeError:
                # Fall back to the older parameter name (api_key)
                self.llm = ChatGroq(
                    temperature=temperature,
                    model_name=model_name,
                    api_key=api_key
                )

            logger.info(f"Initialized Groq LLM with model {model_name}")

        elif provider == "openai":
            if not OPENAI_AVAILABLE:
                raise ImportError("OpenAI integration not available. Install with 'pip install langchain-openai'")

            # Lazy import to avoid memory issues
            from langchain_openai import ChatOpenAI

            self.llm = ChatOpenAI(
                temperature=config.get("temperature", 0.7),
                model_name=config.get("model", "gpt-3.5-turbo"),
                api_key=config.get("api_key", os.getenv("OPENAI_API_KEY", ""))
            )
            logger.info(f"Initialized OpenAI LLM with model {config.get('model', 'gpt-3.5-turbo')}")

        else:
            raise ValueError(f"Unsupported LLM provider: {provider}")

        # Load prompt templates
        if "prompt_templates" in config:
            for name, template in config["prompt_templates"].items():
                self.prompt_templates[name] = PromptTemplate(template)
                logger.debug(f"Loaded prompt template '{name}'")

        # Set default prompt name
        self.default_prompt_name = config.get("default_prompt", "default")

        # Ensure we have at least one prompt template
        if not self.prompt_templates and "default_prompt_template" in config:
            self.prompt_templates["default"] = PromptTemplate(config["default_prompt_template"])
            logger.debug("Loaded default prompt template")

    def get_prompt(self, name: str = None, **kwargs) -> str:
        """
        Get a formatted prompt by name.

        Args:
            name: Name of the prompt template (uses default if None)
            **kwargs: Variables to substitute in the template

        Returns:
            Formatted prompt
        """
        if name is None:
            name = self.default_prompt_name

        if name not in self.prompt_templates:
            logger.warning(f"Prompt template '{name}' not found, using default")
            name = "default"
            if name not in self.prompt_templates:
                logger.error(f"Default prompt template not found")
                return f"Error: No prompt template found for '{name}'"

        return self.prompt_templates[name].format(**kwargs)

    async def process(self, context: "AgentProcessingContext") -> "AgentProcessingContext":
        """
        Process a request using this component.

        Args:
            context: AgentProcessingContext object containing request data and state.

        Returns:
            Updated AgentProcessingContext object.
        """
        if not self.llm:
            logger.error("LLM not initialized")
            context.response = "I'm sorry, but my language model is not properly configured."
            context.add_error(self.name, "llm_not_initialized")
            return context

        # Get the user message from the typed context
        user_message = context.message
        if not user_message: # Also check if context.response is already set by a previous component
            if not context.response: # Only set this if no prior response exists
                 logger.warning("Empty user message and no prior response in context.")
                 context.response = "I didn't receive any message to process."
            return context # Return early if message is empty or response already exists

        # Check if we should use a specific prompt template from metadata or initial_context
        prompt_name = context.metadata.get("prompt_name",
                                           context.initial_context.get("prompt_name", self.default_prompt_name))

        # Prepare prompt variables
        prompt_vars = {
            "message": user_message,
            "user_id": str(context.user_id), # Ensure string for formatting
            "conversation_id": context.conversation_id,
        }

        # Add any additional context variables from initial_context or component_data
        prompt_vars.update(context.initial_context)
        prompt_vars.update(context.component_data.get(self.name, {})) # Component specific data for this prompt

        # Format the prompt
        try:
            prompt = self.get_prompt(prompt_name, **prompt_vars)
        except Exception as e:
            logger.error(f"Error formatting prompt '{prompt_name}': {e}", exc_info=True)
            context.response = "I encountered an error while preparing your request."
            context.add_error(self.name, f"prompt_format_error: {str(e)}", {"prompt_name": prompt_name})
            return context

        # Call the LLM
        try:
            logger.debug(f"Calling LLM (provider: {self.config.get('provider')}, model: {self.config.get('model')}) with prompt: {prompt[:200]}...") # Increased preview
            llm_response = self.llm.invoke(prompt) # Langchain v0.1+ uses invoke
            response_text = llm_response.content

            context.response = response_text # Set the main response
            context.metadata["llm_provider"] = self.config.get("provider", "unknown")
            context.metadata["llm_model"] = self.config.get("model", "unknown")
            # Optionally store raw LLM response or other details in component_data
            # context.set_component_data(self.name, {"raw_llm_response": llm_response.dict()})


            logger.debug(f"LLM response: {response_text[:100]}...")
            return context

        except Exception as e:
            logger.error(f"Error calling LLM: {e}", exc_info=True)
            context.response = "I encountered an error while generating a response."
            context.add_error(self.name, f"llm_error: {str(e)}")
            return context

    def get_capabilities(self) -> List[str]:
        """
        Get the capabilities provided by this component.

        Returns:
            List of capability strings
        """
        return ["llm_processing"] + self.config.get("capabilities", [])
