"""
PandasAI v3 analysis MCP tool.

This module provides an MCP-compatible tool for analyzing data using PandasAI v3.
It integrates with mem0ai for enhanced data analysis capabilities.
"""

import logging
import os
import pandas as pd
from typing import Dict, Any, Optional, List
import base64

from .base import BaseMCPTool
from ..pandasai_v3.wrapper import PandasAIWrapper
from ..pandasai_v3.cache import ResponseCache
from ..pandasai_v3.error_handler import <PERSON><PERSON>r<PERSON><PERSON><PERSON>
from ..chart_optimization import chart_enhancer
from ...utils.memory_service import MemoryService
from ...utils.vector_service import VectorService
from agents.utils.agent_identity import detect_agent_identity
from agents.utils.system_prompts import get_agent_system_prompt

logger = logging.getLogger(__name__)

class PandasAIAnalysisTool(BaseMCPTool):
    """Tool for analyzing data using PandasAI v3."""

    def __init__(self):
        """Initialize the PandasAI analysis tool."""
        super().__init__(
            name="pandasai_analysis",
            description="Analyze data using PandasAI v3 with mem0ai integration",
            input_schema={
                "type": "object",
                "properties": {
                    "file_path": {"type": "string"},
                    "data_source": {
                        "type": ["object", "string"],
                        "description": "Data source information (can be an object with id/name or a string identifier)"
                    },
                    "query": {"type": "string"},
                    "api_key": {"type": "string"},
                    "provider": {"type": "string", "default": "openai"},
                    "model": {"type": "string"},
                    "user_id": {"type": "string"},
                    "persona_id": {"type": "string"},
                    "conversation_id": {"type": "string"},
                    "store_in_memory": {"type": "boolean", "default": True}
                },
                "required": ["query"]  # Removed api_key from required to allow fallbacks
            }
        )
        self.pandasai = PandasAIWrapper()
        self.cache = ResponseCache()
        self.memory_service = MemoryService()
        self.vector_service = VectorService()

    async def execute(self, arguments: Dict[str, Any]) -> Dict[str, Any]:
        """Execute the PandasAI analysis tool with agent-aware capabilities."""
        file_path = arguments.get("file_path")
        data_source = arguments.get("data_source")
        query = arguments.get("query")
        api_key = arguments.get("api_key")
        provider = arguments.get("provider", "openai")

        # Detect agent identity for personalized analysis
        agent_id = arguments.get("persona_id") or arguments.get("agent_id")
        context = arguments.get("context", {})
        agent_identity = await detect_agent_identity(
            agent_id=agent_id,
            context=context,
            intent_type="data_analysis"
        )

        logger.info(f"Detected agent identity: {agent_identity} for PandasAI analysis")

        # Get API key from environment if not provided
        if not api_key:
            # PandasAI requires PANDASAI_API_KEY
            api_key = os.getenv("PANDASAI_API_KEY")

            if not api_key:
                return {
                    "isError": True,
                    "content": [{"type": "text", "text": "No PANDASAI_API_KEY found in environment variables. Please set PANDASAI_API_KEY to use PandasAI."}]
                }

            logger.info("Using PANDASAI_API_KEY from environment")

        # If we have a data_source but no file_path, use the data_access tool to get the file
        if data_source and not file_path:
            try:
                # Import the data access tool
                from .data_access import DataAccessTool

                # Create and initialize the tool
                data_tool = DataAccessTool()
                await data_tool.initialize({})

                # Call the tool to load the data
                data_result = await data_tool.execute({
                    "data_source": data_source,
                    "operation": "load",
                    "params": {"create_sample": True}
                })

                # Check if we got a valid result
                if not data_result.get("isError", False) and "metadata" in data_result:
                    # Extract the file path from the result
                    file_path = data_result["metadata"].get("file_path")
                    logger.info(f"Retrieved file path from data_access tool: {file_path}")
                else:
                    logger.error(f"Error retrieving file path from data_access tool: {data_result}")
                    return {
                        "isError": True,
                        "content": [{"type": "text", "text": "Could not access the data source. Please provide a valid file path or data source."}]
                    }
            except Exception as e:
                logger.error(f"Error using data_access tool: {e}", exc_info=True)
                return {
                    "isError": True,
                    "content": [{"type": "text", "text": f"Error accessing data source: {str(e)}"}]
                }

        # Ensure we have a file path
        if not file_path:
            return {
                "isError": True,
                "content": [{"type": "text", "text": "No file path or valid data source provided"}]
            }

        # Check cache first
        cached_result = self.cache.get(file_path, query, provider)
        if cached_result:
            logger.info(f"Using cached result for query: {query}")
            return cached_result

        try:
            # Initialize PandasAI
            self.pandasai.initialize(api_key, provider)

            # Load dataframe
            if not self.pandasai.load_dataframe(file_path):
                return {
                    "isError": True,
                    "content": [{"type": "text", "text": f"Error loading dataframe from {file_path}"}]
                }

            # Create agent with model if provided
            model = arguments.get("model")
            if not self.pandasai.create_agent(model=model):
                return {
                    "isError": True,
                    "content": [{"type": "text", "text": "Error creating PandasAI Agent"}]
                }

            # Enhance query with agent-specific analysis style
            enhanced_query = await self._enhance_query_with_agent_style(query, agent_identity)

            # Chat with agent using enhanced query
            result = self.pandasai.chat(enhanced_query)

            # Handle error
            if "error" in result:
                return {
                    "isError": True,
                    "content": [{"type": "text", "text": f"Error in PandasAI chat: {result['error']}"}]
                }

            # Format response based on result type
            response = self._format_response(result, query, provider)

            # Cache the response
            self.cache.set(file_path, query, provider, response)

            # Store the analysis in memory if requested
            store_in_memory = arguments.get("store_in_memory", True)
            if store_in_memory:
                user_id = arguments.get("user_id", "system")
                persona_id = arguments.get("persona_id", "unknown")
                conversation_id = arguments.get("conversation_id", "unknown")

                # Create metadata for the memory
                metadata = {
                    "type": "analysis",
                    "file_path": file_path,
                    "query": query,
                    "provider": provider,
                    "persona_id": persona_id,
                    "conversation_id": conversation_id,
                    "analysis_type": "pandasai"
                }

                # Format the content for memory storage
                if "content" in response:
                    content_text = ""
                    for content_item in response["content"]:
                        if content_item.get("type") == "text":
                            content_text += content_item.get("text", "") + "\n\n"

                    # Store in memory
                    if content_text:
                        try:
                            self.memory_service.add_memory(
                                content=f"Analysis of {os.path.basename(file_path)}: {query}\n\n{content_text}",
                                user_id=user_id,
                                metadata=metadata
                            )
                            logger.info(f"Stored analysis in memory for user {user_id}")
                        except Exception as e:
                            logger.error(f"Error storing analysis in memory: {e}")

            return response

        except Exception as e:
            logger.error(f"Error executing PandasAI analysis tool: {e}", exc_info=True)
            return {
                "isError": True,
                "content": [{"type": "text", "text": f"Error: {str(e)}"}]
            }

    def _format_response(self, result: Dict[str, Any], query: str, provider: str = "openai") -> Dict[str, Any]:
        """Format the response for the MCP tool."""
        if result["type"] == "dataframe":
            return {
                "isError": False,
                "content": [
                    {"type": "text", "text": f"Analysis results for: {query}"},
                    {"type": "table", "data": result["data"], "columns": result["columns"]}
                ]
            }
        elif result["type"] == "chart":
            # Use enhanced chart processing for better performance and UX
            try:
                image_path = result["image_path"]
                logger.info(f"Processing chart with enhanced optimization: {image_path}")

                if not os.path.exists(image_path):
                    logger.error(f"Chart image file does not exist: {image_path}")
                    return {
                        "isError": True,
                        "content": [{"type": "text", "text": "The visualization was generated but the image file could not be found."}]
                    }

                # Use the enhanced chart response system
                enhanced_response = chart_enhancer.enhance_chart_response(
                    image_path=image_path,
                    query=query,
                    provider=provider,
                    optimize=True  # Enable optimization for better performance
                )

                # If enhancement succeeded, return the enhanced response
                if not enhanced_response.get("isError", False):
                    logger.info("Successfully enhanced chart response with optimization")
                    return enhanced_response
                else:
                    # Fallback to basic processing if enhancement fails
                    logger.warning("Chart enhancement failed, using basic processing")
                    with open(image_path, "rb") as image_file:
                        encoded_image = base64.b64encode(image_file.read()).decode("utf-8")

                    return {
                        "isError": False,
                        "content": [
                            {"type": "text", "text": f"📊 Visualization for: {query}"},
                            {"type": "image", "src": f"data:image/png;base64,{encoded_image}"}
                        ]
                    }
            except Exception as e:
                logger.error(f"Error reading chart image: {e}", exc_info=True)
                return {
                    "isError": False,
                    "content": [
                        {"type": "text", "text": f"Visualization for: {query}"},
                        {"type": "text", "text": f"Error displaying chart: {str(e)}"}
                    ]
                }
        elif result["type"] == "text":
            text_content = result.get("text", str(result))

            # Check if the text contains tabular data that should be parsed
            parsed_content = self._parse_text_content(text_content)

            return {
                "isError": False,
                "content": parsed_content
            }
        else:
            # Handle unknown types gracefully
            logger.warning(f"Unknown result type: {result.get('type', 'unknown')}")
            return {
                "isError": True,
                "content": [{"type": "text", "text": f"Type '{result.get('type', 'unknown')}' has not been implemented. Result: {str(result)}"}]
            }

    def _parse_text_content(self, text: str) -> List[Dict[str, Any]]:
        """Parse text content to extract tables and format properly."""
        content = []
        lines = text.split('\n')
        current_text = []
        current_table = None

        i = 0
        while i < len(lines):
            line = lines[i].strip()

            # Check if this line looks like a table header (contains |)
            if '|' in line and len(line.split('|')) > 2:
                # Save any accumulated text
                if current_text:
                    text_content = '\n'.join(current_text).strip()
                    if text_content:
                        content.append({"type": "text", "text": text_content})
                    current_text = []

                # Parse the table
                table_lines = [line]
                i += 1

                # Look for separator line (optional)
                if i < len(lines) and '|' in lines[i] and ('-' in lines[i] or '=' in lines[i]):
                    table_lines.append(lines[i])
                    i += 1

                # Collect table rows
                while i < len(lines) and '|' in lines[i] and len(lines[i].split('|')) > 2:
                    table_lines.append(lines[i])
                    i += 1

                # Parse the table data
                parsed_table = self._parse_table_lines(table_lines)
                if parsed_table:
                    content.append({
                        "type": "table",
                        "data": parsed_table["data"],
                        "columns": parsed_table["columns"]
                    })

                continue

            # Regular text line
            current_text.append(line)
            i += 1

        # Add any remaining text
        if current_text:
            text_content = '\n'.join(current_text).strip()
            if text_content:
                content.append({"type": "text", "text": text_content})

        # If no structured content was found, return the original text
        if not content:
            content = [{"type": "text", "text": text}]

        return content

    def _parse_table_lines(self, table_lines: List[str]) -> Dict[str, Any]:
        """Parse table lines into structured data."""
        if not table_lines:
            return None

        # Extract headers from first line
        header_line = table_lines[0]
        headers = [col.strip() for col in header_line.split('|') if col.strip()]

        # Skip separator line if present
        data_start = 1
        if len(table_lines) > 1 and ('-' in table_lines[1] or '=' in table_lines[1]):
            data_start = 2

        # Extract data rows
        data = []
        for line in table_lines[data_start:]:
            if '|' in line:
                row = [col.strip() for col in line.split('|') if col.strip()]
                if len(row) == len(headers):  # Only add rows with correct column count
                    data.append(row)

        if not data:
            return None

        return {
            "columns": headers,
            "data": data
        }

    async def _enhance_query_with_agent_style(self, query: str, agent_identity: str) -> str:
        """Enhance the query with agent-specific analysis style and preferences."""
        try:
            # Get agent system prompt to extract analysis preferences
            system_prompt = await get_agent_system_prompt(agent_identity)

            # Extract analysis style preferences from system prompt
            analysis_style = await self._extract_analysis_style_from_prompt(system_prompt, agent_identity)

            # Enhance query based on agent style
            enhanced_query = self._apply_agent_style_to_query(query, analysis_style, agent_identity)

            logger.info(f"Enhanced query for {agent_identity}: {enhanced_query[:100]}...")
            return enhanced_query

        except Exception as e:
            logger.warning(f"Failed to enhance query with agent style: {e}")
            return query  # Return original query if enhancement fails

    async def _extract_analysis_style_from_prompt(self, system_prompt: str, agent_identity: str) -> Dict[str, Any]:
        """Extract analysis style preferences from agent system prompt."""
        style_preferences = {
            "explanation_level": "detailed",
            "focus_areas": [],
            "presentation_style": "professional",
            "technical_depth": "moderate"
        }

        if not system_prompt:
            return self._get_default_style_for_agent(agent_identity)

        # Look for analysis-related patterns in the system prompt
        import re

        # Check for explanation preferences
        if re.search(r"educational|explain|teaching|learning", system_prompt, re.IGNORECASE):
            style_preferences["explanation_level"] = "educational"
        elif re.search(r"concise|brief|summary", system_prompt, re.IGNORECASE):
            style_preferences["explanation_level"] = "concise"

        # Check for technical depth preferences
        if re.search(r"technical|advanced|statistical|complex", system_prompt, re.IGNORECASE):
            style_preferences["technical_depth"] = "advanced"
        elif re.search(r"simple|basic|accessible", system_prompt, re.IGNORECASE):
            style_preferences["technical_depth"] = "basic"

        # Extract focus areas based on capabilities
        focus_areas = []
        if re.search(r"visualization|chart|graph|plot", system_prompt, re.IGNORECASE):
            focus_areas.append("visualization")
        if re.search(r"statistical|statistics|analysis", system_prompt, re.IGNORECASE):
            focus_areas.append("statistical_analysis")
        if re.search(r"insight|pattern|trend", system_prompt, re.IGNORECASE):
            focus_areas.append("insights")
        if re.search(r"marketing|campaign|strategy", system_prompt, re.IGNORECASE):
            focus_areas.append("marketing_metrics")

        style_preferences["focus_areas"] = focus_areas

        return style_preferences

    def _get_default_style_for_agent(self, agent_identity: str) -> Dict[str, Any]:
        """Get default analysis style for specific agent types."""
        default_styles = {
            "analyst": {
                "explanation_level": "detailed",
                "focus_areas": ["statistical_analysis", "insights", "visualization"],
                "presentation_style": "analytical",
                "technical_depth": "advanced"
            },
            "marketer": {
                "explanation_level": "business_focused",
                "focus_areas": ["marketing_metrics", "insights", "visualization"],
                "presentation_style": "business",
                "technical_depth": "moderate"
            },
            "classifier": {
                "explanation_level": "structured",
                "focus_areas": ["categorization", "patterns"],
                "presentation_style": "organized",
                "technical_depth": "moderate"
            },
            "concierge": {
                "explanation_level": "accessible",
                "focus_areas": ["insights", "visualization"],
                "presentation_style": "friendly",
                "technical_depth": "basic"
            }
        }

        return default_styles.get(agent_identity, {
            "explanation_level": "detailed",
            "focus_areas": ["insights"],
            "presentation_style": "professional",
            "technical_depth": "moderate"
        })

    def _apply_agent_style_to_query(self, query: str, style: Dict[str, Any], agent_identity: str) -> str:
        """Apply agent-specific style to the analysis query."""
        enhanced_query = query

        # Add style-specific instructions based on agent identity
        style_instructions = []

        # Add explanation level instructions
        explanation_level = style.get("explanation_level", "detailed")
        if explanation_level == "educational":
            style_instructions.append("Provide educational explanations of the analysis methods used.")
        elif explanation_level == "business_focused":
            style_instructions.append("Focus on business implications and actionable insights.")
        elif explanation_level == "concise":
            style_instructions.append("Provide concise, summary-focused results.")
        elif explanation_level == "accessible":
            style_instructions.append("Use accessible language and clear explanations.")

        # Add focus area instructions
        focus_areas = style.get("focus_areas", [])
        if "marketing_metrics" in focus_areas:
            style_instructions.append("Include relevant marketing metrics and KPIs in the analysis.")
        if "statistical_analysis" in focus_areas:
            style_instructions.append("Include statistical significance and confidence intervals where applicable.")
        if "visualization" in focus_areas:
            style_instructions.append("Create appropriate visualizations to illustrate the findings.")

        # Add technical depth instructions
        technical_depth = style.get("technical_depth", "moderate")
        if technical_depth == "advanced":
            style_instructions.append("Include advanced statistical methods and technical details.")
        elif technical_depth == "basic":
            style_instructions.append("Keep technical complexity minimal and focus on clear insights.")

        # Combine original query with style instructions
        if style_instructions:
            enhanced_query = f"{query}\n\nAnalysis Style Instructions:\n" + "\n".join([f"- {instruction}" for instruction in style_instructions])

        return enhanced_query
