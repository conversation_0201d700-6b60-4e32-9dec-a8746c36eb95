import {
  identity,
  select_default,
  zoom_default
} from "./chunk-CEXS4FTN.js";
import {
  require_prop_types
} from "./chunk-NHY3NUFE.js";
import "./chunk-AUXQ2FKN.js";
import {
  require_react_dom
} from "./chunk-F34GCA6J.js";
import {
  require_react
} from "./chunk-ZMLY2J2T.js";
import {
  __commonJS,
  __esm,
  __export,
  __toCommonJS,
  __toESM
} from "./chunk-4B2QHNJT.js";

// node_modules/clone/clone.js
var require_clone = __commonJS({
  "node_modules/clone/clone.js"(exports, module) {
    var clone2 = function() {
      "use strict";
      function _instanceof(obj, type) {
        return type != null && obj instanceof type;
      }
      var nativeMap;
      try {
        nativeMap = Map;
      } catch (_) {
        nativeMap = function() {
        };
      }
      var nativeSet;
      try {
        nativeSet = Set;
      } catch (_) {
        nativeSet = function() {
        };
      }
      var nativePromise;
      try {
        nativePromise = Promise;
      } catch (_) {
        nativePromise = function() {
        };
      }
      function clone3(parent, circular, depth, prototype, includeNonEnumerable) {
        if (typeof circular === "object") {
          depth = circular.depth;
          prototype = circular.prototype;
          includeNonEnumerable = circular.includeNonEnumerable;
          circular = circular.circular;
        }
        var allParents = [];
        var allChildren = [];
        var useBuffer = typeof Buffer != "undefined";
        if (typeof circular == "undefined")
          circular = true;
        if (typeof depth == "undefined")
          depth = Infinity;
        function _clone(parent2, depth2) {
          if (parent2 === null)
            return null;
          if (depth2 === 0)
            return parent2;
          var child;
          var proto;
          if (typeof parent2 != "object") {
            return parent2;
          }
          if (_instanceof(parent2, nativeMap)) {
            child = new nativeMap();
          } else if (_instanceof(parent2, nativeSet)) {
            child = new nativeSet();
          } else if (_instanceof(parent2, nativePromise)) {
            child = new nativePromise(function(resolve, reject) {
              parent2.then(function(value) {
                resolve(_clone(value, depth2 - 1));
              }, function(err) {
                reject(_clone(err, depth2 - 1));
              });
            });
          } else if (clone3.__isArray(parent2)) {
            child = [];
          } else if (clone3.__isRegExp(parent2)) {
            child = new RegExp(parent2.source, __getRegExpFlags(parent2));
            if (parent2.lastIndex) child.lastIndex = parent2.lastIndex;
          } else if (clone3.__isDate(parent2)) {
            child = new Date(parent2.getTime());
          } else if (useBuffer && Buffer.isBuffer(parent2)) {
            if (Buffer.allocUnsafe) {
              child = Buffer.allocUnsafe(parent2.length);
            } else {
              child = new Buffer(parent2.length);
            }
            parent2.copy(child);
            return child;
          } else if (_instanceof(parent2, Error)) {
            child = Object.create(parent2);
          } else {
            if (typeof prototype == "undefined") {
              proto = Object.getPrototypeOf(parent2);
              child = Object.create(proto);
            } else {
              child = Object.create(prototype);
              proto = prototype;
            }
          }
          if (circular) {
            var index = allParents.indexOf(parent2);
            if (index != -1) {
              return allChildren[index];
            }
            allParents.push(parent2);
            allChildren.push(child);
          }
          if (_instanceof(parent2, nativeMap)) {
            parent2.forEach(function(value, key) {
              var keyChild = _clone(key, depth2 - 1);
              var valueChild = _clone(value, depth2 - 1);
              child.set(keyChild, valueChild);
            });
          }
          if (_instanceof(parent2, nativeSet)) {
            parent2.forEach(function(value) {
              var entryChild = _clone(value, depth2 - 1);
              child.add(entryChild);
            });
          }
          for (var i in parent2) {
            var attrs;
            if (proto) {
              attrs = Object.getOwnPropertyDescriptor(proto, i);
            }
            if (attrs && attrs.set == null) {
              continue;
            }
            child[i] = _clone(parent2[i], depth2 - 1);
          }
          if (Object.getOwnPropertySymbols) {
            var symbols2 = Object.getOwnPropertySymbols(parent2);
            for (var i = 0; i < symbols2.length; i++) {
              var symbol = symbols2[i];
              var descriptor = Object.getOwnPropertyDescriptor(parent2, symbol);
              if (descriptor && !descriptor.enumerable && !includeNonEnumerable) {
                continue;
              }
              child[symbol] = _clone(parent2[symbol], depth2 - 1);
              if (!descriptor.enumerable) {
                Object.defineProperty(child, symbol, {
                  enumerable: false
                });
              }
            }
          }
          if (includeNonEnumerable) {
            var allPropertyNames = Object.getOwnPropertyNames(parent2);
            for (var i = 0; i < allPropertyNames.length; i++) {
              var propertyName = allPropertyNames[i];
              var descriptor = Object.getOwnPropertyDescriptor(parent2, propertyName);
              if (descriptor && descriptor.enumerable) {
                continue;
              }
              child[propertyName] = _clone(parent2[propertyName], depth2 - 1);
              Object.defineProperty(child, propertyName, {
                enumerable: false
              });
            }
          }
          return child;
        }
        return _clone(parent, depth);
      }
      clone3.clonePrototype = function clonePrototype(parent) {
        if (parent === null)
          return null;
        var c = function() {
        };
        c.prototype = parent;
        return new c();
      };
      function __objToStr(o) {
        return Object.prototype.toString.call(o);
      }
      clone3.__objToStr = __objToStr;
      function __isDate(o) {
        return typeof o === "object" && __objToStr(o) === "[object Date]";
      }
      clone3.__isDate = __isDate;
      function __isArray(o) {
        return typeof o === "object" && __objToStr(o) === "[object Array]";
      }
      clone3.__isArray = __isArray;
      function __isRegExp(o) {
        return typeof o === "object" && __objToStr(o) === "[object RegExp]";
      }
      clone3.__isRegExp = __isRegExp;
      function __getRegExpFlags(re) {
        var flags = "";
        if (re.global) flags += "g";
        if (re.ignoreCase) flags += "i";
        if (re.multiline) flags += "m";
        return flags;
      }
      clone3.__getRegExpFlags = __getRegExpFlags;
      return clone3;
    }();
    if (typeof module === "object" && module.exports) {
      module.exports = clone2;
    }
  }
});

// node_modules/chain-function/index.js
var require_chain_function = __commonJS({
  "node_modules/chain-function/index.js"(exports, module) {
    module.exports = function chain() {
      var len = arguments.length;
      var args = [];
      for (var i = 0; i < len; i++)
        args[i] = arguments[i];
      args = args.filter(function(fn) {
        return fn != null;
      });
      if (args.length === 0) return void 0;
      if (args.length === 1) return args[0];
      return args.reduce(function(current, next) {
        return function chainedFunction() {
          current.apply(this, arguments);
          next.apply(this, arguments);
        };
      });
    };
  }
});

// node_modules/warning/browser.js
var require_browser = __commonJS({
  "node_modules/warning/browser.js"(exports, module) {
    "use strict";
    var warning = function() {
    };
    if (true) {
      warning = function(condition, format, args) {
        var len = arguments.length;
        args = new Array(len > 2 ? len - 2 : 0);
        for (var key = 2; key < len; key++) {
          args[key - 2] = arguments[key];
        }
        if (format === void 0) {
          throw new Error(
            "`warning(condition, format, ...args)` requires a warning message argument"
          );
        }
        if (format.length < 10 || /^[s\W]*$/.test(format)) {
          throw new Error(
            "The warning format should be able to uniquely identify this warning. Please, use a more descriptive format than: " + format
          );
        }
        if (!condition) {
          var argIndex = 0;
          var message = "Warning: " + format.replace(/%s/g, function() {
            return args[argIndex++];
          });
          if (typeof console !== "undefined") {
            console.error(message);
          }
          try {
            throw new Error(message);
          } catch (x2) {
          }
        }
      };
    }
    module.exports = warning;
  }
});

// node_modules/react-lifecycles-compat/react-lifecycles-compat.es.js
var react_lifecycles_compat_es_exports = {};
__export(react_lifecycles_compat_es_exports, {
  polyfill: () => polyfill
});
function componentWillMount() {
  var state = this.constructor.getDerivedStateFromProps(this.props, this.state);
  if (state !== null && state !== void 0) {
    this.setState(state);
  }
}
function componentWillReceiveProps(nextProps) {
  function updater(prevState) {
    var state = this.constructor.getDerivedStateFromProps(nextProps, prevState);
    return state !== null && state !== void 0 ? state : null;
  }
  this.setState(updater.bind(this));
}
function componentWillUpdate(nextProps, nextState) {
  try {
    var prevProps = this.props;
    var prevState = this.state;
    this.props = nextProps;
    this.state = nextState;
    this.__reactInternalSnapshotFlag = true;
    this.__reactInternalSnapshot = this.getSnapshotBeforeUpdate(
      prevProps,
      prevState
    );
  } finally {
    this.props = prevProps;
    this.state = prevState;
  }
}
function polyfill(Component) {
  var prototype = Component.prototype;
  if (!prototype || !prototype.isReactComponent) {
    throw new Error("Can only polyfill class components");
  }
  if (typeof Component.getDerivedStateFromProps !== "function" && typeof prototype.getSnapshotBeforeUpdate !== "function") {
    return Component;
  }
  var foundWillMountName = null;
  var foundWillReceivePropsName = null;
  var foundWillUpdateName = null;
  if (typeof prototype.componentWillMount === "function") {
    foundWillMountName = "componentWillMount";
  } else if (typeof prototype.UNSAFE_componentWillMount === "function") {
    foundWillMountName = "UNSAFE_componentWillMount";
  }
  if (typeof prototype.componentWillReceiveProps === "function") {
    foundWillReceivePropsName = "componentWillReceiveProps";
  } else if (typeof prototype.UNSAFE_componentWillReceiveProps === "function") {
    foundWillReceivePropsName = "UNSAFE_componentWillReceiveProps";
  }
  if (typeof prototype.componentWillUpdate === "function") {
    foundWillUpdateName = "componentWillUpdate";
  } else if (typeof prototype.UNSAFE_componentWillUpdate === "function") {
    foundWillUpdateName = "UNSAFE_componentWillUpdate";
  }
  if (foundWillMountName !== null || foundWillReceivePropsName !== null || foundWillUpdateName !== null) {
    var componentName = Component.displayName || Component.name;
    var newApiName = typeof Component.getDerivedStateFromProps === "function" ? "getDerivedStateFromProps()" : "getSnapshotBeforeUpdate()";
    throw Error(
      "Unsafe legacy lifecycles will not be called for components using new component APIs.\n\n" + componentName + " uses " + newApiName + " but also contains the following legacy lifecycles:" + (foundWillMountName !== null ? "\n  " + foundWillMountName : "") + (foundWillReceivePropsName !== null ? "\n  " + foundWillReceivePropsName : "") + (foundWillUpdateName !== null ? "\n  " + foundWillUpdateName : "") + "\n\nThe above lifecycles should be removed. Learn more about this warning here:\nhttps://fb.me/react-async-component-lifecycle-hooks"
    );
  }
  if (typeof Component.getDerivedStateFromProps === "function") {
    prototype.componentWillMount = componentWillMount;
    prototype.componentWillReceiveProps = componentWillReceiveProps;
  }
  if (typeof prototype.getSnapshotBeforeUpdate === "function") {
    if (typeof prototype.componentDidUpdate !== "function") {
      throw new Error(
        "Cannot polyfill getSnapshotBeforeUpdate() for components that do not define componentDidUpdate() on the prototype"
      );
    }
    prototype.componentWillUpdate = componentWillUpdate;
    var componentDidUpdate = prototype.componentDidUpdate;
    prototype.componentDidUpdate = function componentDidUpdatePolyfill(prevProps, prevState, maybeSnapshot) {
      var snapshot = this.__reactInternalSnapshotFlag ? this.__reactInternalSnapshot : maybeSnapshot;
      componentDidUpdate.call(this, prevProps, prevState, snapshot);
    };
  }
  return Component;
}
var init_react_lifecycles_compat_es = __esm({
  "node_modules/react-lifecycles-compat/react-lifecycles-compat.es.js"() {
    componentWillMount.__suppressDeprecationWarning = true;
    componentWillReceiveProps.__suppressDeprecationWarning = true;
    componentWillUpdate.__suppressDeprecationWarning = true;
  }
});

// node_modules/@bkrem/react-transition-group/utils/ChildMapping.js
var require_ChildMapping = __commonJS({
  "node_modules/@bkrem/react-transition-group/utils/ChildMapping.js"(exports) {
    "use strict";
    exports.__esModule = true;
    exports.getChildMapping = getChildMapping;
    exports.mergeChildMappings = mergeChildMappings;
    var _react = require_react();
    function getChildMapping(children) {
      if (!children) {
        return children;
      }
      var result = {};
      _react.Children.map(children, function(child) {
        return child;
      }).forEach(function(child) {
        result[child.key] = child;
      });
      return result;
    }
    function mergeChildMappings(prev, next) {
      prev = prev || {};
      next = next || {};
      function getValueForKey(key) {
        if (next.hasOwnProperty(key)) {
          return next[key];
        }
        return prev[key];
      }
      var nextKeysPending = {};
      var pendingKeys = [];
      for (var prevKey in prev) {
        if (next.hasOwnProperty(prevKey)) {
          if (pendingKeys.length) {
            nextKeysPending[prevKey] = pendingKeys;
            pendingKeys = [];
          }
        } else {
          pendingKeys.push(prevKey);
        }
      }
      var i = void 0;
      var childMapping = {};
      for (var nextKey in next) {
        if (nextKeysPending.hasOwnProperty(nextKey)) {
          for (i = 0; i < nextKeysPending[nextKey].length; i++) {
            var pendingNextKey = nextKeysPending[nextKey][i];
            childMapping[nextKeysPending[nextKey][i]] = getValueForKey(pendingNextKey);
          }
        }
        childMapping[nextKey] = getValueForKey(nextKey);
      }
      for (i = 0; i < pendingKeys.length; i++) {
        childMapping[pendingKeys[i]] = getValueForKey(pendingKeys[i]);
      }
      return childMapping;
    }
  }
});

// node_modules/@bkrem/react-transition-group/TransitionGroup.js
var require_TransitionGroup = __commonJS({
  "node_modules/@bkrem/react-transition-group/TransitionGroup.js"(exports, module) {
    "use strict";
    exports.__esModule = true;
    var _extends = Object.assign || function(target) {
      for (var i = 1; i < arguments.length; i++) {
        var source = arguments[i];
        for (var key in source) {
          if (Object.prototype.hasOwnProperty.call(source, key)) {
            target[key] = source[key];
          }
        }
      }
      return target;
    };
    var _chainFunction = require_chain_function();
    var _chainFunction2 = _interopRequireDefault(_chainFunction);
    var _react = require_react();
    var _react2 = _interopRequireDefault(_react);
    var _propTypes = require_prop_types();
    var _propTypes2 = _interopRequireDefault(_propTypes);
    var _warning = require_browser();
    var _warning2 = _interopRequireDefault(_warning);
    var _reactLifecyclesCompat = (init_react_lifecycles_compat_es(), __toCommonJS(react_lifecycles_compat_es_exports));
    var _ChildMapping = require_ChildMapping();
    function _interopRequireDefault(obj) {
      return obj && obj.__esModule ? obj : { default: obj };
    }
    function _classCallCheck(instance, Constructor) {
      if (!(instance instanceof Constructor)) {
        throw new TypeError("Cannot call a class as a function");
      }
    }
    function _possibleConstructorReturn(self, call) {
      if (!self) {
        throw new ReferenceError("this hasn't been initialised - super() hasn't been called");
      }
      return call && (typeof call === "object" || typeof call === "function") ? call : self;
    }
    function _inherits(subClass, superClass) {
      if (typeof superClass !== "function" && superClass !== null) {
        throw new TypeError("Super expression must either be null or a function, not " + typeof superClass);
      }
      subClass.prototype = Object.create(superClass && superClass.prototype, { constructor: { value: subClass, enumerable: false, writable: true, configurable: true } });
      if (superClass) Object.setPrototypeOf ? Object.setPrototypeOf(subClass, superClass) : subClass.__proto__ = superClass;
    }
    var propTypes = {
      component: _propTypes2.default.any,
      childFactory: _propTypes2.default.func,
      children: _propTypes2.default.node
    };
    var defaultProps = {
      component: "span",
      childFactory: function childFactory(child) {
        return child;
      }
    };
    var TransitionGroup2 = function(_React$Component) {
      _inherits(TransitionGroup3, _React$Component);
      function TransitionGroup3(props, context) {
        _classCallCheck(this, TransitionGroup3);
        var _this = _possibleConstructorReturn(this, _React$Component.call(this, props, context));
        _this.performAppear = function(key, component) {
          _this.currentlyTransitioningKeys[key] = true;
          if (component.componentWillAppear) {
            component.componentWillAppear(_this._handleDoneAppearing.bind(_this, key, component));
          } else {
            _this._handleDoneAppearing(key, component);
          }
        };
        _this._handleDoneAppearing = function(key, component) {
          if (component && component.componentDidAppear) {
            component.componentDidAppear();
          }
          delete _this.currentlyTransitioningKeys[key];
          var currentChildMapping = (0, _ChildMapping.getChildMapping)(_this.props.children);
          if (!currentChildMapping || !currentChildMapping.hasOwnProperty(key)) {
            _this.performLeave(key, component);
          }
        };
        _this.performEnter = function(key, component) {
          _this.currentlyTransitioningKeys[key] = true;
          if (component.componentWillEnter) {
            component.componentWillEnter(_this._handleDoneEntering.bind(_this, key, component));
          } else {
            _this._handleDoneEntering(key, component);
          }
        };
        _this._handleDoneEntering = function(key, component) {
          if (component && component.componentDidEnter) {
            component.componentDidEnter();
          }
          delete _this.currentlyTransitioningKeys[key];
          var currentChildMapping = (0, _ChildMapping.getChildMapping)(_this.props.children);
          if (!currentChildMapping || !currentChildMapping.hasOwnProperty(key)) {
            _this.performLeave(key, component);
          }
        };
        _this.performLeave = function(key, component) {
          _this.currentlyTransitioningKeys[key] = true;
          if (component && component.componentWillLeave) {
            component.componentWillLeave(_this._handleDoneLeaving.bind(_this, key, component));
          } else {
            _this._handleDoneLeaving(key, component);
          }
        };
        _this._handleDoneLeaving = function(key, component) {
          if (component && component.componentDidLeave) {
            component.componentDidLeave();
          }
          delete _this.currentlyTransitioningKeys[key];
          var currentChildMapping = (0, _ChildMapping.getChildMapping)(_this.props.children);
          if (currentChildMapping && currentChildMapping.hasOwnProperty(key)) {
            _this.keysToEnter.push(key);
          } else {
            _this.setState(function(state) {
              var newChildren = _extends({}, state.children);
              delete newChildren[key];
              return { children: newChildren };
            });
          }
        };
        _this.childRefs = /* @__PURE__ */ Object.create(null);
        _this.currentlyTransitioningKeys = {};
        _this.keysToEnter = [];
        _this.keysToLeave = [];
        _this.state = {
          children: (0, _ChildMapping.getChildMapping)(props.children)
        };
        return _this;
      }
      TransitionGroup3.prototype.componentDidMount = function componentDidMount() {
        var initialChildMapping = this.state.children;
        for (var key in initialChildMapping) {
          if (initialChildMapping[key]) {
            this.performAppear(key, this.childRefs[key]);
          }
        }
      };
      TransitionGroup3.getDerivedStateFromProps = function getDerivedStateFromProps(props, state) {
        var nextChildMapping = (0, _ChildMapping.getChildMapping)(props.children);
        var prevChildMapping = state.children;
        return {
          children: (0, _ChildMapping.mergeChildMappings)(prevChildMapping, nextChildMapping)
        };
      };
      TransitionGroup3.prototype.componentDidUpdate = function componentDidUpdate(prevProps, prevState) {
        var _this2 = this;
        var nextChildMapping = (0, _ChildMapping.getChildMapping)(this.props.children);
        var prevChildMapping = prevState.children;
        for (var key in nextChildMapping) {
          var hasPrev = prevChildMapping && prevChildMapping.hasOwnProperty(key);
          if (nextChildMapping[key] && !hasPrev && !this.currentlyTransitioningKeys[key]) {
            this.keysToEnter.push(key);
          }
        }
        for (var _key in prevChildMapping) {
          var hasNext = nextChildMapping && nextChildMapping.hasOwnProperty(_key);
          if (prevChildMapping[_key] && !hasNext && !this.currentlyTransitioningKeys[_key]) {
            this.keysToLeave.push(_key);
          }
        }
        var keysToEnter = this.keysToEnter;
        this.keysToEnter = [];
        keysToEnter.forEach(function(key2) {
          return _this2.performEnter(key2, _this2.childRefs[key2]);
        });
        var keysToLeave = this.keysToLeave;
        this.keysToLeave = [];
        keysToLeave.forEach(function(key2) {
          return _this2.performLeave(key2, _this2.childRefs[key2]);
        });
      };
      TransitionGroup3.prototype.render = function render() {
        var _this3 = this;
        var childrenToRender = [];
        var _loop = function _loop2(key2) {
          var child = _this3.state.children[key2];
          if (child) {
            var isCallbackRef = typeof child.ref !== "string";
            var factoryChild = _this3.props.childFactory(child);
            var ref = function ref2(r) {
              _this3.childRefs[key2] = r;
            };
            true ? (0, _warning2.default)(isCallbackRef, "string refs are not supported on children of TransitionGroup and will be ignored. Please use a callback ref instead: https://facebook.github.io/react/docs/refs-and-the-dom.html#the-ref-callback-attribute") : void 0;
            if (factoryChild === child && isCallbackRef) {
              ref = (0, _chainFunction2.default)(child.ref, ref);
            }
            childrenToRender.push(_react2.default.cloneElement(factoryChild, {
              key: key2,
              ref
            }));
          }
        };
        for (var key in this.state.children) {
          _loop(key);
        }
        var props = _extends({}, this.props);
        delete props.transitionLeave;
        delete props.transitionName;
        delete props.transitionAppear;
        delete props.transitionEnter;
        delete props.childFactory;
        delete props.transitionLeaveTimeout;
        delete props.transitionEnterTimeout;
        delete props.transitionAppearTimeout;
        delete props.component;
        return _react2.default.createElement(this.props.component, props, childrenToRender);
      };
      return TransitionGroup3;
    }(_react2.default.Component);
    TransitionGroup2.displayName = "TransitionGroup";
    TransitionGroup2.propTypes = true ? propTypes : {};
    TransitionGroup2.defaultProps = defaultProps;
    exports.default = (0, _reactLifecyclesCompat.polyfill)(TransitionGroup2);
    module.exports = exports["default"];
  }
});

// node_modules/@babel/runtime/helpers/interopRequireDefault.js
var require_interopRequireDefault = __commonJS({
  "node_modules/@babel/runtime/helpers/interopRequireDefault.js"(exports, module) {
    function _interopRequireDefault(e) {
      return e && e.__esModule ? e : {
        "default": e
      };
    }
    module.exports = _interopRequireDefault, module.exports.__esModule = true, module.exports["default"] = module.exports;
  }
});

// node_modules/@bkrem/react-transition-group/node_modules/dom-helpers/class/hasClass.js
var require_hasClass = __commonJS({
  "node_modules/@bkrem/react-transition-group/node_modules/dom-helpers/class/hasClass.js"(exports, module) {
    "use strict";
    exports.__esModule = true;
    exports.default = hasClass;
    function hasClass(element, className) {
      if (element.classList) return !!className && element.classList.contains(className);
      else return (" " + (element.className.baseVal || element.className) + " ").indexOf(" " + className + " ") !== -1;
    }
    module.exports = exports["default"];
  }
});

// node_modules/@bkrem/react-transition-group/node_modules/dom-helpers/class/addClass.js
var require_addClass = __commonJS({
  "node_modules/@bkrem/react-transition-group/node_modules/dom-helpers/class/addClass.js"(exports, module) {
    "use strict";
    var _interopRequireDefault = require_interopRequireDefault();
    exports.__esModule = true;
    exports.default = addClass;
    var _hasClass = _interopRequireDefault(require_hasClass());
    function addClass(element, className) {
      if (element.classList) element.classList.add(className);
      else if (!(0, _hasClass.default)(element, className)) if (typeof element.className === "string") element.className = element.className + " " + className;
      else element.setAttribute("class", (element.className && element.className.baseVal || "") + " " + className);
    }
    module.exports = exports["default"];
  }
});

// node_modules/@bkrem/react-transition-group/node_modules/dom-helpers/class/removeClass.js
var require_removeClass = __commonJS({
  "node_modules/@bkrem/react-transition-group/node_modules/dom-helpers/class/removeClass.js"(exports, module) {
    "use strict";
    function replaceClassName(origClass, classToRemove) {
      return origClass.replace(new RegExp("(^|\\s)" + classToRemove + "(?:\\s|$)", "g"), "$1").replace(/\s+/g, " ").replace(/^\s*|\s*$/g, "");
    }
    module.exports = function removeClass(element, className) {
      if (element.classList) element.classList.remove(className);
      else if (typeof element.className === "string") element.className = replaceClassName(element.className, className);
      else element.setAttribute("class", replaceClassName(element.className && element.className.baseVal || "", className));
    };
  }
});

// node_modules/@bkrem/react-transition-group/node_modules/dom-helpers/util/inDOM.js
var require_inDOM = __commonJS({
  "node_modules/@bkrem/react-transition-group/node_modules/dom-helpers/util/inDOM.js"(exports, module) {
    "use strict";
    exports.__esModule = true;
    exports.default = void 0;
    var _default = !!(typeof window !== "undefined" && window.document && window.document.createElement);
    exports.default = _default;
    module.exports = exports["default"];
  }
});

// node_modules/@bkrem/react-transition-group/node_modules/dom-helpers/util/requestAnimationFrame.js
var require_requestAnimationFrame = __commonJS({
  "node_modules/@bkrem/react-transition-group/node_modules/dom-helpers/util/requestAnimationFrame.js"(exports, module) {
    "use strict";
    var _interopRequireDefault = require_interopRequireDefault();
    exports.__esModule = true;
    exports.default = void 0;
    var _inDOM = _interopRequireDefault(require_inDOM());
    var vendors = ["", "webkit", "moz", "o", "ms"];
    var cancel = "clearTimeout";
    var raf = fallback;
    var compatRaf;
    var getKey = function getKey2(vendor, k2) {
      return vendor + (!vendor ? k2 : k2[0].toUpperCase() + k2.substr(1)) + "AnimationFrame";
    };
    if (_inDOM.default) {
      vendors.some(function(vendor) {
        var rafKey = getKey(vendor, "request");
        if (rafKey in window) {
          cancel = getKey(vendor, "cancel");
          return raf = function raf2(cb) {
            return window[rafKey](cb);
          };
        }
      });
    }
    var prev = (/* @__PURE__ */ new Date()).getTime();
    function fallback(fn) {
      var curr = (/* @__PURE__ */ new Date()).getTime(), ms = Math.max(0, 16 - (curr - prev)), req = setTimeout(fn, ms);
      prev = curr;
      return req;
    }
    compatRaf = function compatRaf2(cb) {
      return raf(cb);
    };
    compatRaf.cancel = function(id) {
      window[cancel] && typeof window[cancel] === "function" && window[cancel](id);
    };
    var _default = compatRaf;
    exports.default = _default;
    module.exports = exports["default"];
  }
});

// node_modules/@bkrem/react-transition-group/node_modules/dom-helpers/transition/properties.js
var require_properties = __commonJS({
  "node_modules/@bkrem/react-transition-group/node_modules/dom-helpers/transition/properties.js"(exports) {
    "use strict";
    var _interopRequireDefault = require_interopRequireDefault();
    exports.__esModule = true;
    exports.default = exports.animationEnd = exports.animationDelay = exports.animationTiming = exports.animationDuration = exports.animationName = exports.transitionEnd = exports.transitionDuration = exports.transitionDelay = exports.transitionTiming = exports.transitionProperty = exports.transform = void 0;
    var _inDOM = _interopRequireDefault(require_inDOM());
    var transform = "transform";
    exports.transform = transform;
    var prefix;
    var transitionEnd;
    var animationEnd;
    exports.animationEnd = animationEnd;
    exports.transitionEnd = transitionEnd;
    var transitionProperty;
    var transitionDuration;
    var transitionTiming;
    var transitionDelay;
    exports.transitionDelay = transitionDelay;
    exports.transitionTiming = transitionTiming;
    exports.transitionDuration = transitionDuration;
    exports.transitionProperty = transitionProperty;
    var animationName;
    var animationDuration;
    var animationTiming;
    var animationDelay;
    exports.animationDelay = animationDelay;
    exports.animationTiming = animationTiming;
    exports.animationDuration = animationDuration;
    exports.animationName = animationName;
    if (_inDOM.default) {
      _getTransitionPropert = getTransitionProperties();
      prefix = _getTransitionPropert.prefix;
      exports.transitionEnd = transitionEnd = _getTransitionPropert.transitionEnd;
      exports.animationEnd = animationEnd = _getTransitionPropert.animationEnd;
      exports.transform = transform = prefix + "-" + transform;
      exports.transitionProperty = transitionProperty = prefix + "-transition-property";
      exports.transitionDuration = transitionDuration = prefix + "-transition-duration";
      exports.transitionDelay = transitionDelay = prefix + "-transition-delay";
      exports.transitionTiming = transitionTiming = prefix + "-transition-timing-function";
      exports.animationName = animationName = prefix + "-animation-name";
      exports.animationDuration = animationDuration = prefix + "-animation-duration";
      exports.animationTiming = animationTiming = prefix + "-animation-delay";
      exports.animationDelay = animationDelay = prefix + "-animation-timing-function";
    }
    var _getTransitionPropert;
    var _default = {
      transform,
      end: transitionEnd,
      property: transitionProperty,
      timing: transitionTiming,
      delay: transitionDelay,
      duration: transitionDuration
    };
    exports.default = _default;
    function getTransitionProperties() {
      var style = document.createElement("div").style;
      var vendorMap = {
        O: function O(e) {
          return "o" + e.toLowerCase();
        },
        Moz: function Moz(e) {
          return e.toLowerCase();
        },
        Webkit: function Webkit(e) {
          return "webkit" + e;
        },
        ms: function ms(e) {
          return "MS" + e;
        }
      };
      var vendors = Object.keys(vendorMap);
      var transitionEnd2, animationEnd2;
      var prefix2 = "";
      for (var i = 0; i < vendors.length; i++) {
        var vendor = vendors[i];
        if (vendor + "TransitionProperty" in style) {
          prefix2 = "-" + vendor.toLowerCase();
          transitionEnd2 = vendorMap[vendor]("TransitionEnd");
          animationEnd2 = vendorMap[vendor]("AnimationEnd");
          break;
        }
      }
      if (!transitionEnd2 && "transitionProperty" in style) transitionEnd2 = "transitionend";
      if (!animationEnd2 && "animationName" in style) animationEnd2 = "animationend";
      style = null;
      return {
        animationEnd: animationEnd2,
        transitionEnd: transitionEnd2,
        prefix: prefix2
      };
    }
  }
});

// node_modules/@bkrem/react-transition-group/utils/PropTypes.js
var require_PropTypes = __commonJS({
  "node_modules/@bkrem/react-transition-group/utils/PropTypes.js"(exports) {
    "use strict";
    exports.__esModule = true;
    exports.nameShape = void 0;
    exports.transitionTimeout = transitionTimeout;
    var _react = require_react();
    var _react2 = _interopRequireDefault(_react);
    var _propTypes = require_prop_types();
    var _propTypes2 = _interopRequireDefault(_propTypes);
    function _interopRequireDefault(obj) {
      return obj && obj.__esModule ? obj : { default: obj };
    }
    function transitionTimeout(transitionType) {
      var timeoutPropName = "transition" + transitionType + "Timeout";
      var enabledPropName = "transition" + transitionType;
      return function(props) {
        if (props[enabledPropName]) {
          if (props[timeoutPropName] == null) {
            return new Error(timeoutPropName + " wasn't supplied to CSSTransitionGroup: this can cause unreliable animations and won't be supported in a future version of React. See https://fb.me/react-animation-transition-group-timeout for more information.");
          } else if (typeof props[timeoutPropName] !== "number") {
            return new Error(timeoutPropName + " must be a number (in milliseconds)");
          }
        }
        return null;
      };
    }
    var nameShape = exports.nameShape = _propTypes2.default.oneOfType([_propTypes2.default.string, _propTypes2.default.shape({
      enter: _propTypes2.default.string,
      leave: _propTypes2.default.string,
      active: _propTypes2.default.string
    }), _propTypes2.default.shape({
      enter: _propTypes2.default.string,
      enterActive: _propTypes2.default.string,
      leave: _propTypes2.default.string,
      leaveActive: _propTypes2.default.string,
      appear: _propTypes2.default.string,
      appearActive: _propTypes2.default.string
    })]);
  }
});

// node_modules/@bkrem/react-transition-group/CSSTransitionGroupChild.js
var require_CSSTransitionGroupChild = __commonJS({
  "node_modules/@bkrem/react-transition-group/CSSTransitionGroupChild.js"(exports, module) {
    "use strict";
    exports.__esModule = true;
    var _extends = Object.assign || function(target) {
      for (var i = 1; i < arguments.length; i++) {
        var source = arguments[i];
        for (var key in source) {
          if (Object.prototype.hasOwnProperty.call(source, key)) {
            target[key] = source[key];
          }
        }
      }
      return target;
    };
    var _addClass = require_addClass();
    var _addClass2 = _interopRequireDefault(_addClass);
    var _removeClass = require_removeClass();
    var _removeClass2 = _interopRequireDefault(_removeClass);
    var _requestAnimationFrame = require_requestAnimationFrame();
    var _requestAnimationFrame2 = _interopRequireDefault(_requestAnimationFrame);
    var _properties = require_properties();
    var _react = require_react();
    var _react2 = _interopRequireDefault(_react);
    var _propTypes = require_prop_types();
    var _propTypes2 = _interopRequireDefault(_propTypes);
    var _reactDom = require_react_dom();
    var _PropTypes = require_PropTypes();
    function _interopRequireDefault(obj) {
      return obj && obj.__esModule ? obj : { default: obj };
    }
    function _classCallCheck(instance, Constructor) {
      if (!(instance instanceof Constructor)) {
        throw new TypeError("Cannot call a class as a function");
      }
    }
    function _possibleConstructorReturn(self, call) {
      if (!self) {
        throw new ReferenceError("this hasn't been initialised - super() hasn't been called");
      }
      return call && (typeof call === "object" || typeof call === "function") ? call : self;
    }
    function _inherits(subClass, superClass) {
      if (typeof superClass !== "function" && superClass !== null) {
        throw new TypeError("Super expression must either be null or a function, not " + typeof superClass);
      }
      subClass.prototype = Object.create(superClass && superClass.prototype, { constructor: { value: subClass, enumerable: false, writable: true, configurable: true } });
      if (superClass) Object.setPrototypeOf ? Object.setPrototypeOf(subClass, superClass) : subClass.__proto__ = superClass;
    }
    var events = [];
    if (_properties.transitionEnd) events.push(_properties.transitionEnd);
    if (_properties.animationEnd) events.push(_properties.animationEnd);
    function addEndListener(node, listener) {
      if (events.length) {
        events.forEach(function(e) {
          return node.addEventListener(e, listener, false);
        });
      } else {
        setTimeout(listener, 0);
      }
      return function() {
        if (!events.length) return;
        events.forEach(function(e) {
          return node.removeEventListener(e, listener, false);
        });
      };
    }
    var propTypes = {
      children: _propTypes2.default.node,
      name: _PropTypes.nameShape.isRequired,
      // Once we require timeouts to be specified, we can remove the
      // boolean flags (appear etc.) and just accept a number
      // or a bool for the timeout flags (appearTimeout etc.)
      appear: _propTypes2.default.bool,
      enter: _propTypes2.default.bool,
      leave: _propTypes2.default.bool,
      appearTimeout: _propTypes2.default.number,
      enterTimeout: _propTypes2.default.number,
      leaveTimeout: _propTypes2.default.number
    };
    var CSSTransitionGroupChild = function(_React$Component) {
      _inherits(CSSTransitionGroupChild2, _React$Component);
      function CSSTransitionGroupChild2(props, context) {
        _classCallCheck(this, CSSTransitionGroupChild2);
        var _this = _possibleConstructorReturn(this, _React$Component.call(this, props, context));
        _this.componentWillAppear = function(done) {
          if (_this.props.appear) {
            _this.transition("appear", done, _this.props.appearTimeout);
          } else {
            done();
          }
        };
        _this.componentWillEnter = function(done) {
          if (_this.props.enter) {
            _this.transition("enter", done, _this.props.enterTimeout);
          } else {
            done();
          }
        };
        _this.componentWillLeave = function(done) {
          if (_this.props.leave) {
            _this.transition("leave", done, _this.props.leaveTimeout);
          } else {
            done();
          }
        };
        _this.classNameAndNodeQueue = [];
        _this.transitionTimeouts = [];
        return _this;
      }
      CSSTransitionGroupChild2.prototype.componentWillUnmount = function componentWillUnmount() {
        this.unmounted = true;
        if (this.timeout) {
          clearTimeout(this.timeout);
        }
        this.transitionTimeouts.forEach(function(timeout) {
          clearTimeout(timeout);
        });
        this.classNameAndNodeQueue.length = 0;
      };
      CSSTransitionGroupChild2.prototype.transition = function transition(animationType, finishCallback, timeout) {
        var node = (0, _reactDom.findDOMNode)(this);
        if (!node) {
          if (finishCallback) {
            finishCallback();
          }
          return;
        }
        var className = this.props.name[animationType] || this.props.name + "-" + animationType;
        var activeClassName = this.props.name[animationType + "Active"] || className + "-active";
        var timer = null;
        var removeListeners = void 0;
        (0, _addClass2.default)(node, className);
        this.queueClassAndNode(activeClassName, node);
        var finish = function finish2(e) {
          if (e && e.target !== node) {
            return;
          }
          clearTimeout(timer);
          if (removeListeners) removeListeners();
          (0, _removeClass2.default)(node, className);
          (0, _removeClass2.default)(node, activeClassName);
          if (removeListeners) removeListeners();
          if (finishCallback) {
            finishCallback();
          }
        };
        if (timeout) {
          timer = setTimeout(finish, timeout);
          this.transitionTimeouts.push(timer);
        } else if (_properties.transitionEnd) {
          removeListeners = addEndListener(node, finish);
        }
      };
      CSSTransitionGroupChild2.prototype.queueClassAndNode = function queueClassAndNode(className, node) {
        var _this2 = this;
        this.classNameAndNodeQueue.push({
          className,
          node
        });
        if (!this.rafHandle) {
          this.rafHandle = (0, _requestAnimationFrame2.default)(function() {
            return _this2.flushClassNameAndNodeQueue();
          });
        }
      };
      CSSTransitionGroupChild2.prototype.flushClassNameAndNodeQueue = function flushClassNameAndNodeQueue() {
        if (!this.unmounted) {
          this.classNameAndNodeQueue.forEach(function(obj) {
            obj.node.scrollTop;
            (0, _addClass2.default)(obj.node, obj.className);
          });
        }
        this.classNameAndNodeQueue.length = 0;
        this.rafHandle = null;
      };
      CSSTransitionGroupChild2.prototype.render = function render() {
        var props = _extends({}, this.props);
        delete props.name;
        delete props.appear;
        delete props.enter;
        delete props.leave;
        delete props.appearTimeout;
        delete props.enterTimeout;
        delete props.leaveTimeout;
        delete props.children;
        return _react2.default.cloneElement(_react2.default.Children.only(this.props.children), props);
      };
      return CSSTransitionGroupChild2;
    }(_react2.default.Component);
    CSSTransitionGroupChild.displayName = "CSSTransitionGroupChild";
    CSSTransitionGroupChild.propTypes = true ? propTypes : {};
    exports.default = CSSTransitionGroupChild;
    module.exports = exports["default"];
  }
});

// node_modules/@bkrem/react-transition-group/CSSTransitionGroup.js
var require_CSSTransitionGroup = __commonJS({
  "node_modules/@bkrem/react-transition-group/CSSTransitionGroup.js"(exports, module) {
    "use strict";
    exports.__esModule = true;
    var _extends = Object.assign || function(target) {
      for (var i = 1; i < arguments.length; i++) {
        var source = arguments[i];
        for (var key in source) {
          if (Object.prototype.hasOwnProperty.call(source, key)) {
            target[key] = source[key];
          }
        }
      }
      return target;
    };
    var _react = require_react();
    var _react2 = _interopRequireDefault(_react);
    var _propTypes = require_prop_types();
    var _propTypes2 = _interopRequireDefault(_propTypes);
    var _TransitionGroup = require_TransitionGroup();
    var _TransitionGroup2 = _interopRequireDefault(_TransitionGroup);
    var _CSSTransitionGroupChild = require_CSSTransitionGroupChild();
    var _CSSTransitionGroupChild2 = _interopRequireDefault(_CSSTransitionGroupChild);
    var _PropTypes = require_PropTypes();
    function _interopRequireDefault(obj) {
      return obj && obj.__esModule ? obj : { default: obj };
    }
    function _classCallCheck(instance, Constructor) {
      if (!(instance instanceof Constructor)) {
        throw new TypeError("Cannot call a class as a function");
      }
    }
    function _possibleConstructorReturn(self, call) {
      if (!self) {
        throw new ReferenceError("this hasn't been initialised - super() hasn't been called");
      }
      return call && (typeof call === "object" || typeof call === "function") ? call : self;
    }
    function _inherits(subClass, superClass) {
      if (typeof superClass !== "function" && superClass !== null) {
        throw new TypeError("Super expression must either be null or a function, not " + typeof superClass);
      }
      subClass.prototype = Object.create(superClass && superClass.prototype, { constructor: { value: subClass, enumerable: false, writable: true, configurable: true } });
      if (superClass) Object.setPrototypeOf ? Object.setPrototypeOf(subClass, superClass) : subClass.__proto__ = superClass;
    }
    var propTypes = {
      transitionName: _PropTypes.nameShape.isRequired,
      transitionAppear: _propTypes2.default.bool,
      transitionEnter: _propTypes2.default.bool,
      transitionLeave: _propTypes2.default.bool,
      transitionAppearTimeout: (0, _PropTypes.transitionTimeout)("Appear"),
      transitionEnterTimeout: (0, _PropTypes.transitionTimeout)("Enter"),
      transitionLeaveTimeout: (0, _PropTypes.transitionTimeout)("Leave")
    };
    var defaultProps = {
      transitionAppear: false,
      transitionEnter: true,
      transitionLeave: true
    };
    var CSSTransitionGroup = function(_React$Component) {
      _inherits(CSSTransitionGroup2, _React$Component);
      function CSSTransitionGroup2() {
        var _temp, _this, _ret;
        _classCallCheck(this, CSSTransitionGroup2);
        for (var _len = arguments.length, args = Array(_len), _key = 0; _key < _len; _key++) {
          args[_key] = arguments[_key];
        }
        return _ret = (_temp = (_this = _possibleConstructorReturn(this, _React$Component.call.apply(_React$Component, [this].concat(args))), _this), _this._wrapChild = function(child) {
          return _react2.default.createElement(_CSSTransitionGroupChild2.default, {
            name: _this.props.transitionName,
            appear: _this.props.transitionAppear,
            enter: _this.props.transitionEnter,
            leave: _this.props.transitionLeave,
            appearTimeout: _this.props.transitionAppearTimeout,
            enterTimeout: _this.props.transitionEnterTimeout,
            leaveTimeout: _this.props.transitionLeaveTimeout
          }, child);
        }, _temp), _possibleConstructorReturn(_this, _ret);
      }
      CSSTransitionGroup2.prototype.render = function render() {
        return _react2.default.createElement(_TransitionGroup2.default, _extends({}, this.props, { childFactory: this._wrapChild }));
      };
      return CSSTransitionGroup2;
    }(_react2.default.Component);
    CSSTransitionGroup.displayName = "CSSTransitionGroup";
    CSSTransitionGroup.propTypes = true ? propTypes : {};
    CSSTransitionGroup.defaultProps = defaultProps;
    exports.default = CSSTransitionGroup;
    module.exports = exports["default"];
  }
});

// node_modules/@bkrem/react-transition-group/index.js
var require_react_transition_group = __commonJS({
  "node_modules/@bkrem/react-transition-group/index.js"(exports, module) {
    "use strict";
    var _CSSTransitionGroup = require_CSSTransitionGroup();
    var _CSSTransitionGroup2 = _interopRequireDefault(_CSSTransitionGroup);
    var _TransitionGroup = require_TransitionGroup();
    var _TransitionGroup2 = _interopRequireDefault(_TransitionGroup);
    function _interopRequireDefault(obj) {
      return obj && obj.__esModule ? obj : { default: obj };
    }
    module.exports = {
      TransitionGroup: _TransitionGroup2.default,
      CSSTransitionGroup: _CSSTransitionGroup2.default
    };
  }
});

// node_modules/react-d3-tree/lib/esm/Tree/index.js
var import_react5 = __toESM(require_react(), 1);

// node_modules/react-d3-tree/node_modules/d3-hierarchy/src/hierarchy/count.js
function count(node) {
  var sum2 = 0, children = node.children, i = children && children.length;
  if (!i) sum2 = 1;
  else while (--i >= 0) sum2 += children[i].value;
  node.value = sum2;
}
function count_default() {
  return this.eachAfter(count);
}

// node_modules/react-d3-tree/node_modules/d3-hierarchy/src/hierarchy/each.js
function each_default(callback) {
  var node = this, current, next = [node], children, i, n;
  do {
    current = next.reverse(), next = [];
    while (node = current.pop()) {
      callback(node), children = node.children;
      if (children) for (i = 0, n = children.length; i < n; ++i) {
        next.push(children[i]);
      }
    }
  } while (next.length);
  return this;
}

// node_modules/react-d3-tree/node_modules/d3-hierarchy/src/hierarchy/eachBefore.js
function eachBefore_default(callback) {
  var node = this, nodes = [node], children, i;
  while (node = nodes.pop()) {
    callback(node), children = node.children;
    if (children) for (i = children.length - 1; i >= 0; --i) {
      nodes.push(children[i]);
    }
  }
  return this;
}

// node_modules/react-d3-tree/node_modules/d3-hierarchy/src/hierarchy/eachAfter.js
function eachAfter_default(callback) {
  var node = this, nodes = [node], next = [], children, i, n;
  while (node = nodes.pop()) {
    next.push(node), children = node.children;
    if (children) for (i = 0, n = children.length; i < n; ++i) {
      nodes.push(children[i]);
    }
  }
  while (node = next.pop()) {
    callback(node);
  }
  return this;
}

// node_modules/react-d3-tree/node_modules/d3-hierarchy/src/hierarchy/sum.js
function sum_default(value) {
  return this.eachAfter(function(node) {
    var sum2 = +value(node.data) || 0, children = node.children, i = children && children.length;
    while (--i >= 0) sum2 += children[i].value;
    node.value = sum2;
  });
}

// node_modules/react-d3-tree/node_modules/d3-hierarchy/src/hierarchy/sort.js
function sort_default(compare) {
  return this.eachBefore(function(node) {
    if (node.children) {
      node.children.sort(compare);
    }
  });
}

// node_modules/react-d3-tree/node_modules/d3-hierarchy/src/hierarchy/path.js
function path_default(end) {
  var start = this, ancestor = leastCommonAncestor(start, end), nodes = [start];
  while (start !== ancestor) {
    start = start.parent;
    nodes.push(start);
  }
  var k2 = nodes.length;
  while (end !== ancestor) {
    nodes.splice(k2, 0, end);
    end = end.parent;
  }
  return nodes;
}
function leastCommonAncestor(a2, b) {
  if (a2 === b) return a2;
  var aNodes = a2.ancestors(), bNodes = b.ancestors(), c = null;
  a2 = aNodes.pop();
  b = bNodes.pop();
  while (a2 === b) {
    c = a2;
    a2 = aNodes.pop();
    b = bNodes.pop();
  }
  return c;
}

// node_modules/react-d3-tree/node_modules/d3-hierarchy/src/hierarchy/ancestors.js
function ancestors_default() {
  var node = this, nodes = [node];
  while (node = node.parent) {
    nodes.push(node);
  }
  return nodes;
}

// node_modules/react-d3-tree/node_modules/d3-hierarchy/src/hierarchy/descendants.js
function descendants_default() {
  var nodes = [];
  this.each(function(node) {
    nodes.push(node);
  });
  return nodes;
}

// node_modules/react-d3-tree/node_modules/d3-hierarchy/src/hierarchy/leaves.js
function leaves_default() {
  var leaves = [];
  this.eachBefore(function(node) {
    if (!node.children) {
      leaves.push(node);
    }
  });
  return leaves;
}

// node_modules/react-d3-tree/node_modules/d3-hierarchy/src/hierarchy/links.js
function links_default() {
  var root = this, links = [];
  root.each(function(node) {
    if (node !== root) {
      links.push({ source: node.parent, target: node });
    }
  });
  return links;
}

// node_modules/react-d3-tree/node_modules/d3-hierarchy/src/hierarchy/index.js
function hierarchy(data, children) {
  var root = new Node(data), valued = +data.value && (root.value = data.value), node, nodes = [root], child, childs, i, n;
  if (children == null) children = defaultChildren;
  while (node = nodes.pop()) {
    if (valued) node.value = +node.data.value;
    if ((childs = children(node.data)) && (n = childs.length)) {
      node.children = new Array(n);
      for (i = n - 1; i >= 0; --i) {
        nodes.push(child = node.children[i] = new Node(childs[i]));
        child.parent = node;
        child.depth = node.depth + 1;
      }
    }
  }
  return root.eachBefore(computeHeight);
}
function node_copy() {
  return hierarchy(this).eachBefore(copyData);
}
function defaultChildren(d) {
  return d.children;
}
function copyData(node) {
  node.data = node.data.data;
}
function computeHeight(node) {
  var height = 0;
  do
    node.height = height;
  while ((node = node.parent) && node.height < ++height);
}
function Node(data) {
  this.data = data;
  this.depth = this.height = 0;
  this.parent = null;
}
Node.prototype = hierarchy.prototype = {
  constructor: Node,
  count: count_default,
  each: each_default,
  eachAfter: eachAfter_default,
  eachBefore: eachBefore_default,
  sum: sum_default,
  sort: sort_default,
  path: path_default,
  ancestors: ancestors_default,
  descendants: descendants_default,
  leaves: leaves_default,
  links: links_default,
  copy: node_copy
};

// node_modules/react-d3-tree/node_modules/d3-hierarchy/src/array.js
var slice = Array.prototype.slice;

// node_modules/react-d3-tree/node_modules/d3-hierarchy/src/treemap/dice.js
function dice_default(parent, x0, y0, x1, y1) {
  var nodes = parent.children, node, i = -1, n = nodes.length, k2 = parent.value && (x1 - x0) / parent.value;
  while (++i < n) {
    node = nodes[i], node.y0 = y0, node.y1 = y1;
    node.x0 = x0, node.x1 = x0 += node.value * k2;
  }
}

// node_modules/react-d3-tree/node_modules/d3-hierarchy/src/tree.js
function defaultSeparation(a2, b) {
  return a2.parent === b.parent ? 1 : 2;
}
function nextLeft(v) {
  var children = v.children;
  return children ? children[0] : v.t;
}
function nextRight(v) {
  var children = v.children;
  return children ? children[children.length - 1] : v.t;
}
function moveSubtree(wm, wp, shift) {
  var change = shift / (wp.i - wm.i);
  wp.c -= change;
  wp.s += shift;
  wm.c += change;
  wp.z += shift;
  wp.m += shift;
}
function executeShifts(v) {
  var shift = 0, change = 0, children = v.children, i = children.length, w;
  while (--i >= 0) {
    w = children[i];
    w.z += shift;
    w.m += shift;
    shift += w.s + (change += w.c);
  }
}
function nextAncestor(vim, v, ancestor) {
  return vim.a.parent === v.parent ? vim.a : ancestor;
}
function TreeNode(node, i) {
  this._ = node;
  this.parent = null;
  this.children = null;
  this.A = null;
  this.a = this;
  this.z = 0;
  this.m = 0;
  this.c = 0;
  this.s = 0;
  this.t = null;
  this.i = i;
}
TreeNode.prototype = Object.create(Node.prototype);
function treeRoot(root) {
  var tree = new TreeNode(root, 0), node, nodes = [tree], child, children, i, n;
  while (node = nodes.pop()) {
    if (children = node._.children) {
      node.children = new Array(n = children.length);
      for (i = n - 1; i >= 0; --i) {
        nodes.push(child = node.children[i] = new TreeNode(children[i], i));
        child.parent = node;
      }
    }
  }
  (tree.parent = new TreeNode(null, 0)).children = [tree];
  return tree;
}
function tree_default() {
  var separation = defaultSeparation, dx = 1, dy = 1, nodeSize = null;
  function tree(root) {
    var t = treeRoot(root);
    t.eachAfter(firstWalk), t.parent.m = -t.z;
    t.eachBefore(secondWalk);
    if (nodeSize) root.eachBefore(sizeNode);
    else {
      var left = root, right = root, bottom = root;
      root.eachBefore(function(node) {
        if (node.x < left.x) left = node;
        if (node.x > right.x) right = node;
        if (node.depth > bottom.depth) bottom = node;
      });
      var s2 = left === right ? 1 : separation(left, right) / 2, tx = s2 - left.x, kx2 = dx / (right.x + s2 + tx), ky2 = dy / (bottom.depth || 1);
      root.eachBefore(function(node) {
        node.x = (node.x + tx) * kx2;
        node.y = node.depth * ky2;
      });
    }
    return root;
  }
  function firstWalk(v) {
    var children = v.children, siblings = v.parent.children, w = v.i ? siblings[v.i - 1] : null;
    if (children) {
      executeShifts(v);
      var midpoint = (children[0].z + children[children.length - 1].z) / 2;
      if (w) {
        v.z = w.z + separation(v._, w._);
        v.m = v.z - midpoint;
      } else {
        v.z = midpoint;
      }
    } else if (w) {
      v.z = w.z + separation(v._, w._);
    }
    v.parent.A = apportion(v, w, v.parent.A || siblings[0]);
  }
  function secondWalk(v) {
    v._.x = v.z + v.parent.m;
    v.m += v.parent.m;
  }
  function apportion(v, w, ancestor) {
    if (w) {
      var vip = v, vop = v, vim = w, vom = vip.parent.children[0], sip = vip.m, sop = vop.m, sim = vim.m, som = vom.m, shift;
      while (vim = nextRight(vim), vip = nextLeft(vip), vim && vip) {
        vom = nextLeft(vom);
        vop = nextRight(vop);
        vop.a = v;
        shift = vim.z + sim - vip.z - sip + separation(vim._, vip._);
        if (shift > 0) {
          moveSubtree(nextAncestor(vim, v, ancestor), v, shift);
          sip += shift;
          sop += shift;
        }
        sim += vim.m;
        sip += vip.m;
        som += vom.m;
        sop += vop.m;
      }
      if (vim && !nextRight(vop)) {
        vop.t = vim;
        vop.m += sim - sop;
      }
      if (vip && !nextLeft(vom)) {
        vom.t = vip;
        vom.m += sip - som;
        ancestor = v;
      }
    }
    return ancestor;
  }
  function sizeNode(node) {
    node.x *= dx;
    node.y = node.depth * dy;
  }
  tree.separation = function(x2) {
    return arguments.length ? (separation = x2, tree) : separation;
  };
  tree.size = function(x2) {
    return arguments.length ? (nodeSize = false, dx = +x2[0], dy = +x2[1], tree) : nodeSize ? null : [dx, dy];
  };
  tree.nodeSize = function(x2) {
    return arguments.length ? (nodeSize = true, dx = +x2[0], dy = +x2[1], tree) : nodeSize ? [dx, dy] : null;
  };
  return tree;
}

// node_modules/react-d3-tree/node_modules/d3-hierarchy/src/treemap/slice.js
function slice_default(parent, x0, y0, x1, y1) {
  var nodes = parent.children, node, i = -1, n = nodes.length, k2 = parent.value && (y1 - y0) / parent.value;
  while (++i < n) {
    node = nodes[i], node.x0 = x0, node.x1 = x1;
    node.y0 = y0, node.y1 = y0 += node.value * k2;
  }
}

// node_modules/react-d3-tree/node_modules/d3-hierarchy/src/treemap/squarify.js
var phi = (1 + Math.sqrt(5)) / 2;
function squarifyRatio(ratio, parent, x0, y0, x1, y1) {
  var rows = [], nodes = parent.children, row, nodeValue, i0 = 0, i1 = 0, n = nodes.length, dx, dy, value = parent.value, sumValue, minValue, maxValue, newRatio, minRatio, alpha, beta;
  while (i0 < n) {
    dx = x1 - x0, dy = y1 - y0;
    do
      sumValue = nodes[i1++].value;
    while (!sumValue && i1 < n);
    minValue = maxValue = sumValue;
    alpha = Math.max(dy / dx, dx / dy) / (value * ratio);
    beta = sumValue * sumValue * alpha;
    minRatio = Math.max(maxValue / beta, beta / minValue);
    for (; i1 < n; ++i1) {
      sumValue += nodeValue = nodes[i1].value;
      if (nodeValue < minValue) minValue = nodeValue;
      if (nodeValue > maxValue) maxValue = nodeValue;
      beta = sumValue * sumValue * alpha;
      newRatio = Math.max(maxValue / beta, beta / minValue);
      if (newRatio > minRatio) {
        sumValue -= nodeValue;
        break;
      }
      minRatio = newRatio;
    }
    rows.push(row = { value: sumValue, dice: dx < dy, children: nodes.slice(i0, i1) });
    if (row.dice) dice_default(row, x0, y0, x1, value ? y0 += dy * sumValue / value : y1);
    else slice_default(row, x0, y0, value ? x0 += dx * sumValue / value : x1, y1);
    value -= sumValue, i0 = i1;
  }
  return rows;
}
var squarify_default = function custom(ratio) {
  function squarify(parent, x0, y0, x1, y1) {
    squarifyRatio(ratio, parent, x0, y0, x1, y1);
  }
  squarify.ratio = function(x2) {
    return custom((x2 = +x2) > 1 ? x2 : 1);
  };
  return squarify;
}(phi);

// node_modules/react-d3-tree/node_modules/d3-hierarchy/src/treemap/resquarify.js
var resquarify_default = function custom2(ratio) {
  function resquarify(parent, x0, y0, x1, y1) {
    if ((rows = parent._squarify) && rows.ratio === ratio) {
      var rows, row, nodes, i, j = -1, n, m = rows.length, value = parent.value;
      while (++j < m) {
        row = rows[j], nodes = row.children;
        for (i = row.value = 0, n = nodes.length; i < n; ++i) row.value += nodes[i].value;
        if (row.dice) dice_default(row, x0, y0, x1, y0 += (y1 - y0) * row.value / value);
        else slice_default(row, x0, y0, x0 += (x1 - x0) * row.value / value, y1);
        value -= row.value;
      }
    } else {
      parent._squarify = rows = squarifyRatio(ratio, parent, x0, y0, x1, y1);
      rows.ratio = ratio;
    }
  }
  resquarify.ratio = function(x2) {
    return custom2((x2 = +x2) > 1 ? x2 : 1);
  };
  return resquarify;
}(phi);

// node_modules/dequal/lite/index.mjs
var has = Object.prototype.hasOwnProperty;
function dequal(foo, bar) {
  var ctor, len;
  if (foo === bar) return true;
  if (foo && bar && (ctor = foo.constructor) === bar.constructor) {
    if (ctor === Date) return foo.getTime() === bar.getTime();
    if (ctor === RegExp) return foo.toString() === bar.toString();
    if (ctor === Array) {
      if ((len = foo.length) === bar.length) {
        while (len-- && dequal(foo[len], bar[len])) ;
      }
      return len === -1;
    }
    if (!ctor || typeof foo === "object") {
      len = 0;
      for (ctor in foo) {
        if (has.call(foo, ctor) && ++len && !has.call(bar, ctor)) return false;
        if (!(ctor in bar) || !dequal(foo[ctor], bar[ctor])) return false;
      }
      return Object.keys(bar).length === len;
    }
  }
  return foo !== foo && bar !== bar;
}

// node_modules/react-d3-tree/lib/esm/Tree/index.js
var import_clone = __toESM(require_clone(), 1);

// node_modules/react-d3-tree/node_modules/uuid/dist/esm-browser/rng.js
var getRandomValues;
var rnds8 = new Uint8Array(16);
function rng() {
  if (!getRandomValues) {
    getRandomValues = typeof crypto !== "undefined" && crypto.getRandomValues && crypto.getRandomValues.bind(crypto) || typeof msCrypto !== "undefined" && typeof msCrypto.getRandomValues === "function" && msCrypto.getRandomValues.bind(msCrypto);
    if (!getRandomValues) {
      throw new Error("crypto.getRandomValues() not supported. See https://github.com/uuidjs/uuid#getrandomvalues-not-supported");
    }
  }
  return getRandomValues(rnds8);
}

// node_modules/react-d3-tree/node_modules/uuid/dist/esm-browser/regex.js
var regex_default = /^(?:[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}|00000000-0000-0000-0000-000000000000)$/i;

// node_modules/react-d3-tree/node_modules/uuid/dist/esm-browser/validate.js
function validate(uuid) {
  return typeof uuid === "string" && regex_default.test(uuid);
}
var validate_default = validate;

// node_modules/react-d3-tree/node_modules/uuid/dist/esm-browser/stringify.js
var byteToHex = [];
for (i = 0; i < 256; ++i) {
  byteToHex.push((i + 256).toString(16).substr(1));
}
var i;
function stringify(arr) {
  var offset = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 0;
  var uuid = (byteToHex[arr[offset + 0]] + byteToHex[arr[offset + 1]] + byteToHex[arr[offset + 2]] + byteToHex[arr[offset + 3]] + "-" + byteToHex[arr[offset + 4]] + byteToHex[arr[offset + 5]] + "-" + byteToHex[arr[offset + 6]] + byteToHex[arr[offset + 7]] + "-" + byteToHex[arr[offset + 8]] + byteToHex[arr[offset + 9]] + "-" + byteToHex[arr[offset + 10]] + byteToHex[arr[offset + 11]] + byteToHex[arr[offset + 12]] + byteToHex[arr[offset + 13]] + byteToHex[arr[offset + 14]] + byteToHex[arr[offset + 15]]).toLowerCase();
  if (!validate_default(uuid)) {
    throw TypeError("Stringified UUID is invalid");
  }
  return uuid;
}
var stringify_default = stringify;

// node_modules/react-d3-tree/node_modules/uuid/dist/esm-browser/parse.js
function parse(uuid) {
  if (!validate_default(uuid)) {
    throw TypeError("Invalid UUID");
  }
  var v;
  var arr = new Uint8Array(16);
  arr[0] = (v = parseInt(uuid.slice(0, 8), 16)) >>> 24;
  arr[1] = v >>> 16 & 255;
  arr[2] = v >>> 8 & 255;
  arr[3] = v & 255;
  arr[4] = (v = parseInt(uuid.slice(9, 13), 16)) >>> 8;
  arr[5] = v & 255;
  arr[6] = (v = parseInt(uuid.slice(14, 18), 16)) >>> 8;
  arr[7] = v & 255;
  arr[8] = (v = parseInt(uuid.slice(19, 23), 16)) >>> 8;
  arr[9] = v & 255;
  arr[10] = (v = parseInt(uuid.slice(24, 36), 16)) / 1099511627776 & 255;
  arr[11] = v / 4294967296 & 255;
  arr[12] = v >>> 24 & 255;
  arr[13] = v >>> 16 & 255;
  arr[14] = v >>> 8 & 255;
  arr[15] = v & 255;
  return arr;
}
var parse_default = parse;

// node_modules/react-d3-tree/node_modules/uuid/dist/esm-browser/v35.js
function stringToBytes(str) {
  str = unescape(encodeURIComponent(str));
  var bytes = [];
  for (var i = 0; i < str.length; ++i) {
    bytes.push(str.charCodeAt(i));
  }
  return bytes;
}
var DNS = "6ba7b810-9dad-11d1-80b4-00c04fd430c8";
var URL = "6ba7b811-9dad-11d1-80b4-00c04fd430c8";
function v35_default(name, version, hashfunc) {
  function generateUUID(value, namespace, buf, offset) {
    if (typeof value === "string") {
      value = stringToBytes(value);
    }
    if (typeof namespace === "string") {
      namespace = parse_default(namespace);
    }
    if (namespace.length !== 16) {
      throw TypeError("Namespace must be array-like (16 iterable integer values, 0-255)");
    }
    var bytes = new Uint8Array(16 + value.length);
    bytes.set(namespace);
    bytes.set(value, namespace.length);
    bytes = hashfunc(bytes);
    bytes[6] = bytes[6] & 15 | version;
    bytes[8] = bytes[8] & 63 | 128;
    if (buf) {
      offset = offset || 0;
      for (var i = 0; i < 16; ++i) {
        buf[offset + i] = bytes[i];
      }
      return buf;
    }
    return stringify_default(bytes);
  }
  try {
    generateUUID.name = name;
  } catch (err) {
  }
  generateUUID.DNS = DNS;
  generateUUID.URL = URL;
  return generateUUID;
}

// node_modules/react-d3-tree/node_modules/uuid/dist/esm-browser/md5.js
function md5(bytes) {
  if (typeof bytes === "string") {
    var msg = unescape(encodeURIComponent(bytes));
    bytes = new Uint8Array(msg.length);
    for (var i = 0; i < msg.length; ++i) {
      bytes[i] = msg.charCodeAt(i);
    }
  }
  return md5ToHexEncodedArray(wordsToMd5(bytesToWords(bytes), bytes.length * 8));
}
function md5ToHexEncodedArray(input) {
  var output = [];
  var length32 = input.length * 32;
  var hexTab = "0123456789abcdef";
  for (var i = 0; i < length32; i += 8) {
    var x2 = input[i >> 5] >>> i % 32 & 255;
    var hex = parseInt(hexTab.charAt(x2 >>> 4 & 15) + hexTab.charAt(x2 & 15), 16);
    output.push(hex);
  }
  return output;
}
function getOutputLength(inputLength8) {
  return (inputLength8 + 64 >>> 9 << 4) + 14 + 1;
}
function wordsToMd5(x2, len) {
  x2[len >> 5] |= 128 << len % 32;
  x2[getOutputLength(len) - 1] = len;
  var a2 = 1732584193;
  var b = -271733879;
  var c = -1732584194;
  var d = 271733878;
  for (var i = 0; i < x2.length; i += 16) {
    var olda = a2;
    var oldb = b;
    var oldc = c;
    var oldd = d;
    a2 = md5ff(a2, b, c, d, x2[i], 7, -680876936);
    d = md5ff(d, a2, b, c, x2[i + 1], 12, -389564586);
    c = md5ff(c, d, a2, b, x2[i + 2], 17, 606105819);
    b = md5ff(b, c, d, a2, x2[i + 3], 22, -1044525330);
    a2 = md5ff(a2, b, c, d, x2[i + 4], 7, -176418897);
    d = md5ff(d, a2, b, c, x2[i + 5], 12, 1200080426);
    c = md5ff(c, d, a2, b, x2[i + 6], 17, -1473231341);
    b = md5ff(b, c, d, a2, x2[i + 7], 22, -45705983);
    a2 = md5ff(a2, b, c, d, x2[i + 8], 7, 1770035416);
    d = md5ff(d, a2, b, c, x2[i + 9], 12, -1958414417);
    c = md5ff(c, d, a2, b, x2[i + 10], 17, -42063);
    b = md5ff(b, c, d, a2, x2[i + 11], 22, -1990404162);
    a2 = md5ff(a2, b, c, d, x2[i + 12], 7, 1804603682);
    d = md5ff(d, a2, b, c, x2[i + 13], 12, -40341101);
    c = md5ff(c, d, a2, b, x2[i + 14], 17, -1502002290);
    b = md5ff(b, c, d, a2, x2[i + 15], 22, 1236535329);
    a2 = md5gg(a2, b, c, d, x2[i + 1], 5, -165796510);
    d = md5gg(d, a2, b, c, x2[i + 6], 9, -1069501632);
    c = md5gg(c, d, a2, b, x2[i + 11], 14, 643717713);
    b = md5gg(b, c, d, a2, x2[i], 20, -373897302);
    a2 = md5gg(a2, b, c, d, x2[i + 5], 5, -701558691);
    d = md5gg(d, a2, b, c, x2[i + 10], 9, 38016083);
    c = md5gg(c, d, a2, b, x2[i + 15], 14, -660478335);
    b = md5gg(b, c, d, a2, x2[i + 4], 20, -405537848);
    a2 = md5gg(a2, b, c, d, x2[i + 9], 5, 568446438);
    d = md5gg(d, a2, b, c, x2[i + 14], 9, -1019803690);
    c = md5gg(c, d, a2, b, x2[i + 3], 14, -187363961);
    b = md5gg(b, c, d, a2, x2[i + 8], 20, 1163531501);
    a2 = md5gg(a2, b, c, d, x2[i + 13], 5, -1444681467);
    d = md5gg(d, a2, b, c, x2[i + 2], 9, -51403784);
    c = md5gg(c, d, a2, b, x2[i + 7], 14, 1735328473);
    b = md5gg(b, c, d, a2, x2[i + 12], 20, -1926607734);
    a2 = md5hh(a2, b, c, d, x2[i + 5], 4, -378558);
    d = md5hh(d, a2, b, c, x2[i + 8], 11, -2022574463);
    c = md5hh(c, d, a2, b, x2[i + 11], 16, 1839030562);
    b = md5hh(b, c, d, a2, x2[i + 14], 23, -35309556);
    a2 = md5hh(a2, b, c, d, x2[i + 1], 4, -1530992060);
    d = md5hh(d, a2, b, c, x2[i + 4], 11, 1272893353);
    c = md5hh(c, d, a2, b, x2[i + 7], 16, -155497632);
    b = md5hh(b, c, d, a2, x2[i + 10], 23, -1094730640);
    a2 = md5hh(a2, b, c, d, x2[i + 13], 4, 681279174);
    d = md5hh(d, a2, b, c, x2[i], 11, -358537222);
    c = md5hh(c, d, a2, b, x2[i + 3], 16, -722521979);
    b = md5hh(b, c, d, a2, x2[i + 6], 23, 76029189);
    a2 = md5hh(a2, b, c, d, x2[i + 9], 4, -640364487);
    d = md5hh(d, a2, b, c, x2[i + 12], 11, -421815835);
    c = md5hh(c, d, a2, b, x2[i + 15], 16, 530742520);
    b = md5hh(b, c, d, a2, x2[i + 2], 23, -995338651);
    a2 = md5ii(a2, b, c, d, x2[i], 6, -198630844);
    d = md5ii(d, a2, b, c, x2[i + 7], 10, 1126891415);
    c = md5ii(c, d, a2, b, x2[i + 14], 15, -1416354905);
    b = md5ii(b, c, d, a2, x2[i + 5], 21, -57434055);
    a2 = md5ii(a2, b, c, d, x2[i + 12], 6, 1700485571);
    d = md5ii(d, a2, b, c, x2[i + 3], 10, -1894986606);
    c = md5ii(c, d, a2, b, x2[i + 10], 15, -1051523);
    b = md5ii(b, c, d, a2, x2[i + 1], 21, -2054922799);
    a2 = md5ii(a2, b, c, d, x2[i + 8], 6, 1873313359);
    d = md5ii(d, a2, b, c, x2[i + 15], 10, -30611744);
    c = md5ii(c, d, a2, b, x2[i + 6], 15, -1560198380);
    b = md5ii(b, c, d, a2, x2[i + 13], 21, 1309151649);
    a2 = md5ii(a2, b, c, d, x2[i + 4], 6, -145523070);
    d = md5ii(d, a2, b, c, x2[i + 11], 10, -1120210379);
    c = md5ii(c, d, a2, b, x2[i + 2], 15, 718787259);
    b = md5ii(b, c, d, a2, x2[i + 9], 21, -343485551);
    a2 = safeAdd(a2, olda);
    b = safeAdd(b, oldb);
    c = safeAdd(c, oldc);
    d = safeAdd(d, oldd);
  }
  return [a2, b, c, d];
}
function bytesToWords(input) {
  if (input.length === 0) {
    return [];
  }
  var length8 = input.length * 8;
  var output = new Uint32Array(getOutputLength(length8));
  for (var i = 0; i < length8; i += 8) {
    output[i >> 5] |= (input[i / 8] & 255) << i % 32;
  }
  return output;
}
function safeAdd(x2, y2) {
  var lsw = (x2 & 65535) + (y2 & 65535);
  var msw = (x2 >> 16) + (y2 >> 16) + (lsw >> 16);
  return msw << 16 | lsw & 65535;
}
function bitRotateLeft(num, cnt) {
  return num << cnt | num >>> 32 - cnt;
}
function md5cmn(q, a2, b, x2, s2, t) {
  return safeAdd(bitRotateLeft(safeAdd(safeAdd(a2, q), safeAdd(x2, t)), s2), b);
}
function md5ff(a2, b, c, d, x2, s2, t) {
  return md5cmn(b & c | ~b & d, a2, b, x2, s2, t);
}
function md5gg(a2, b, c, d, x2, s2, t) {
  return md5cmn(b & d | c & ~d, a2, b, x2, s2, t);
}
function md5hh(a2, b, c, d, x2, s2, t) {
  return md5cmn(b ^ c ^ d, a2, b, x2, s2, t);
}
function md5ii(a2, b, c, d, x2, s2, t) {
  return md5cmn(c ^ (b | ~d), a2, b, x2, s2, t);
}
var md5_default = md5;

// node_modules/react-d3-tree/node_modules/uuid/dist/esm-browser/v3.js
var v3 = v35_default("v3", 48, md5_default);

// node_modules/react-d3-tree/node_modules/uuid/dist/esm-browser/v4.js
function v4(options, buf, offset) {
  options = options || {};
  var rnds = options.random || (options.rng || rng)();
  rnds[6] = rnds[6] & 15 | 64;
  rnds[8] = rnds[8] & 63 | 128;
  if (buf) {
    offset = offset || 0;
    for (var i = 0; i < 16; ++i) {
      buf[offset + i] = rnds[i];
    }
    return buf;
  }
  return stringify_default(rnds);
}
var v4_default = v4;

// node_modules/react-d3-tree/node_modules/uuid/dist/esm-browser/sha1.js
function f(s2, x2, y2, z) {
  switch (s2) {
    case 0:
      return x2 & y2 ^ ~x2 & z;
    case 1:
      return x2 ^ y2 ^ z;
    case 2:
      return x2 & y2 ^ x2 & z ^ y2 & z;
    case 3:
      return x2 ^ y2 ^ z;
  }
}
function ROTL(x2, n) {
  return x2 << n | x2 >>> 32 - n;
}
function sha1(bytes) {
  var K = [1518500249, 1859775393, 2400959708, 3395469782];
  var H = [1732584193, 4023233417, 2562383102, 271733878, 3285377520];
  if (typeof bytes === "string") {
    var msg = unescape(encodeURIComponent(bytes));
    bytes = [];
    for (var i = 0; i < msg.length; ++i) {
      bytes.push(msg.charCodeAt(i));
    }
  } else if (!Array.isArray(bytes)) {
    bytes = Array.prototype.slice.call(bytes);
  }
  bytes.push(128);
  var l = bytes.length / 4 + 2;
  var N = Math.ceil(l / 16);
  var M = new Array(N);
  for (var _i = 0; _i < N; ++_i) {
    var arr = new Uint32Array(16);
    for (var j = 0; j < 16; ++j) {
      arr[j] = bytes[_i * 64 + j * 4] << 24 | bytes[_i * 64 + j * 4 + 1] << 16 | bytes[_i * 64 + j * 4 + 2] << 8 | bytes[_i * 64 + j * 4 + 3];
    }
    M[_i] = arr;
  }
  M[N - 1][14] = (bytes.length - 1) * 8 / Math.pow(2, 32);
  M[N - 1][14] = Math.floor(M[N - 1][14]);
  M[N - 1][15] = (bytes.length - 1) * 8 & 4294967295;
  for (var _i2 = 0; _i2 < N; ++_i2) {
    var W = new Uint32Array(80);
    for (var t = 0; t < 16; ++t) {
      W[t] = M[_i2][t];
    }
    for (var _t = 16; _t < 80; ++_t) {
      W[_t] = ROTL(W[_t - 3] ^ W[_t - 8] ^ W[_t - 14] ^ W[_t - 16], 1);
    }
    var a2 = H[0];
    var b = H[1];
    var c = H[2];
    var d = H[3];
    var e = H[4];
    for (var _t2 = 0; _t2 < 80; ++_t2) {
      var s2 = Math.floor(_t2 / 20);
      var T = ROTL(a2, 5) + f(s2, b, c, d) + e + K[s2] + W[_t2] >>> 0;
      e = d;
      d = c;
      c = ROTL(b, 30) >>> 0;
      b = a2;
      a2 = T;
    }
    H[0] = H[0] + a2 >>> 0;
    H[1] = H[1] + b >>> 0;
    H[2] = H[2] + c >>> 0;
    H[3] = H[3] + d >>> 0;
    H[4] = H[4] + e >>> 0;
  }
  return [H[0] >> 24 & 255, H[0] >> 16 & 255, H[0] >> 8 & 255, H[0] & 255, H[1] >> 24 & 255, H[1] >> 16 & 255, H[1] >> 8 & 255, H[1] & 255, H[2] >> 24 & 255, H[2] >> 16 & 255, H[2] >> 8 & 255, H[2] & 255, H[3] >> 24 & 255, H[3] >> 16 & 255, H[3] >> 8 & 255, H[3] & 255, H[4] >> 24 & 255, H[4] >> 16 & 255, H[4] >> 8 & 255, H[4] & 255];
}
var sha1_default = sha1;

// node_modules/react-d3-tree/node_modules/uuid/dist/esm-browser/v5.js
var v5 = v35_default("v5", 80, sha1_default);

// node_modules/react-d3-tree/lib/esm/Tree/TransitionGroupWrapper.js
var import_react = __toESM(require_react(), 1);
var import_react_transition_group = __toESM(require_react_transition_group(), 1);
var TransitionGroupWrapper = (props) => props.enableLegacyTransitions ? import_react.default.createElement(import_react_transition_group.TransitionGroup, { component: props.component, className: props.className, transform: props.transform }, props.children) : import_react.default.createElement("g", { className: props.className, transform: props.transform }, props.children);
var TransitionGroupWrapper_default = TransitionGroupWrapper;

// node_modules/react-d3-tree/lib/esm/Node/index.js
var import_react3 = __toESM(require_react(), 1);

// node_modules/react-d3-tree/lib/esm/Node/DefaultNodeElement.js
var import_react2 = __toESM(require_react(), 1);
var DEFAULT_NODE_CIRCLE_RADIUS = 15;
var textLayout = {
  title: {
    textAnchor: "start",
    x: 40
  },
  attribute: {
    x: 40,
    dy: "1.2em"
  }
};
var DefaultNodeElement = ({ nodeDatum, toggleNode, onNodeClick, onNodeMouseOver, onNodeMouseOut }) => import_react2.default.createElement(
  import_react2.default.Fragment,
  null,
  import_react2.default.createElement("circle", { r: DEFAULT_NODE_CIRCLE_RADIUS, onClick: (evt) => {
    toggleNode();
    onNodeClick(evt);
  }, onMouseOver: onNodeMouseOver, onMouseOut: onNodeMouseOut }),
  import_react2.default.createElement(
    "g",
    { className: "rd3t-label" },
    import_react2.default.createElement("text", Object.assign({ className: "rd3t-label__title" }, textLayout.title), nodeDatum.name),
    import_react2.default.createElement("text", { className: "rd3t-label__attributes" }, nodeDatum.attributes && Object.entries(nodeDatum.attributes).map(([labelKey, labelValue], i) => import_react2.default.createElement(
      "tspan",
      Object.assign({ key: `${labelKey}-${i}` }, textLayout.attribute),
      labelKey,
      ": ",
      typeof labelValue === "boolean" ? labelValue.toString() : labelValue
    )))
  )
);
var DefaultNodeElement_default = DefaultNodeElement;

// node_modules/react-d3-tree/lib/esm/Node/index.js
var Node2 = class extends import_react3.default.Component {
  constructor() {
    super(...arguments);
    this.nodeRef = null;
    this.state = {
      transform: this.setTransform(this.props.position, this.props.parent, this.props.orientation, true),
      initialStyle: {
        opacity: 0
      },
      wasClicked: false
    };
    this.shouldNodeTransform = (ownProps, nextProps, ownState, nextState) => nextProps.subscriptions !== ownProps.subscriptions || nextProps.position.x !== ownProps.position.x || nextProps.position.y !== ownProps.position.y || nextProps.orientation !== ownProps.orientation || nextState.wasClicked !== ownState.wasClicked;
    this.renderNodeElement = () => {
      const { data, hierarchyPointNode, renderCustomNodeElement } = this.props;
      const renderNode = typeof renderCustomNodeElement === "function" ? renderCustomNodeElement : DefaultNodeElement_default;
      const nodeProps = {
        hierarchyPointNode,
        nodeDatum: data,
        toggleNode: this.handleNodeToggle,
        onNodeClick: this.handleOnClick,
        onNodeMouseOver: this.handleOnMouseOver,
        onNodeMouseOut: this.handleOnMouseOut,
        addChildren: this.handleAddChildren
      };
      return renderNode(nodeProps);
    };
    this.handleNodeToggle = () => {
      this.setState({ wasClicked: true });
      this.props.onNodeToggle(this.props.data.__rd3t.id);
    };
    this.handleOnClick = (evt) => {
      this.setState({ wasClicked: true });
      this.props.onNodeClick(this.props.hierarchyPointNode, evt);
    };
    this.handleOnMouseOver = (evt) => {
      this.props.onNodeMouseOver(this.props.hierarchyPointNode, evt);
    };
    this.handleOnMouseOut = (evt) => {
      this.props.onNodeMouseOut(this.props.hierarchyPointNode, evt);
    };
    this.handleAddChildren = (childrenData) => {
      this.props.handleAddChildrenToNode(this.props.data.__rd3t.id, childrenData);
    };
  }
  componentDidMount() {
    this.commitTransform();
  }
  componentDidUpdate() {
    if (this.state.wasClicked) {
      this.props.centerNode(this.props.hierarchyPointNode);
      this.setState({ wasClicked: false });
    }
    this.commitTransform();
  }
  shouldComponentUpdate(nextProps, nextState) {
    return this.shouldNodeTransform(this.props, nextProps, this.state, nextState);
  }
  setTransform(position, parent, orientation, shouldTranslateToOrigin = false) {
    if (shouldTranslateToOrigin) {
      const hasParent = parent !== null && parent !== void 0;
      const originX = hasParent ? parent.x : 0;
      const originY = hasParent ? parent.y : 0;
      return orientation === "horizontal" ? `translate(${originY},${originX})` : `translate(${originX},${originY})`;
    }
    return orientation === "horizontal" ? `translate(${position.y},${position.x})` : `translate(${position.x},${position.y})`;
  }
  applyTransform(transform, transitionDuration, opacity = 1, done = () => {
  }) {
    if (this.props.enableLegacyTransitions) {
      select_default(this.nodeRef).transition().duration(transitionDuration).attr("transform", transform).style("opacity", opacity).on("end", done);
    } else {
      select_default(this.nodeRef).attr("transform", transform).style("opacity", opacity);
      done();
    }
  }
  commitTransform() {
    const { orientation, transitionDuration, position, parent } = this.props;
    const transform = this.setTransform(position, parent, orientation);
    this.applyTransform(transform, transitionDuration);
  }
  componentWillLeave(done) {
    const { orientation, transitionDuration, position, parent } = this.props;
    const transform = this.setTransform(position, parent, orientation, true);
    this.applyTransform(transform, transitionDuration, 0, done);
  }
  render() {
    const { data, nodeClassName } = this.props;
    return import_react3.default.createElement("g", { id: data.__rd3t.id, ref: (n) => {
      this.nodeRef = n;
    }, style: this.state.initialStyle, className: [
      data.children && data.children.length > 0 ? "rd3t-node" : "rd3t-leaf-node",
      nodeClassName
    ].join(" ").trim(), transform: this.state.transform }, this.renderNodeElement());
  }
};

// node_modules/react-d3-tree/lib/esm/Link/index.js
var import_react4 = __toESM(require_react(), 1);

// node_modules/react-d3-tree/node_modules/d3-path/src/path.js
var pi = Math.PI;
var tau = 2 * pi;
var epsilon = 1e-6;
var tauEpsilon = tau - epsilon;
function Path() {
  this._x0 = this._y0 = // start of current subpath
  this._x1 = this._y1 = null;
  this._ = "";
}
function path() {
  return new Path();
}
Path.prototype = path.prototype = {
  constructor: Path,
  moveTo: function(x2, y2) {
    this._ += "M" + (this._x0 = this._x1 = +x2) + "," + (this._y0 = this._y1 = +y2);
  },
  closePath: function() {
    if (this._x1 !== null) {
      this._x1 = this._x0, this._y1 = this._y0;
      this._ += "Z";
    }
  },
  lineTo: function(x2, y2) {
    this._ += "L" + (this._x1 = +x2) + "," + (this._y1 = +y2);
  },
  quadraticCurveTo: function(x1, y1, x2, y2) {
    this._ += "Q" + +x1 + "," + +y1 + "," + (this._x1 = +x2) + "," + (this._y1 = +y2);
  },
  bezierCurveTo: function(x1, y1, x2, y2, x3, y3) {
    this._ += "C" + +x1 + "," + +y1 + "," + +x2 + "," + +y2 + "," + (this._x1 = +x3) + "," + (this._y1 = +y3);
  },
  arcTo: function(x1, y1, x2, y2, r) {
    x1 = +x1, y1 = +y1, x2 = +x2, y2 = +y2, r = +r;
    var x0 = this._x1, y0 = this._y1, x21 = x2 - x1, y21 = y2 - y1, x01 = x0 - x1, y01 = y0 - y1, l01_2 = x01 * x01 + y01 * y01;
    if (r < 0) throw new Error("negative radius: " + r);
    if (this._x1 === null) {
      this._ += "M" + (this._x1 = x1) + "," + (this._y1 = y1);
    } else if (!(l01_2 > epsilon)) ;
    else if (!(Math.abs(y01 * x21 - y21 * x01) > epsilon) || !r) {
      this._ += "L" + (this._x1 = x1) + "," + (this._y1 = y1);
    } else {
      var x20 = x2 - x0, y20 = y2 - y0, l21_2 = x21 * x21 + y21 * y21, l20_2 = x20 * x20 + y20 * y20, l21 = Math.sqrt(l21_2), l01 = Math.sqrt(l01_2), l = r * Math.tan((pi - Math.acos((l21_2 + l01_2 - l20_2) / (2 * l21 * l01))) / 2), t01 = l / l01, t21 = l / l21;
      if (Math.abs(t01 - 1) > epsilon) {
        this._ += "L" + (x1 + t01 * x01) + "," + (y1 + t01 * y01);
      }
      this._ += "A" + r + "," + r + ",0,0," + +(y01 * x20 > x01 * y20) + "," + (this._x1 = x1 + t21 * x21) + "," + (this._y1 = y1 + t21 * y21);
    }
  },
  arc: function(x2, y2, r, a0, a1, ccw) {
    x2 = +x2, y2 = +y2, r = +r, ccw = !!ccw;
    var dx = r * Math.cos(a0), dy = r * Math.sin(a0), x0 = x2 + dx, y0 = y2 + dy, cw = 1 ^ ccw, da = ccw ? a0 - a1 : a1 - a0;
    if (r < 0) throw new Error("negative radius: " + r);
    if (this._x1 === null) {
      this._ += "M" + x0 + "," + y0;
    } else if (Math.abs(this._x1 - x0) > epsilon || Math.abs(this._y1 - y0) > epsilon) {
      this._ += "L" + x0 + "," + y0;
    }
    if (!r) return;
    if (da < 0) da = da % tau + tau;
    if (da > tauEpsilon) {
      this._ += "A" + r + "," + r + ",0,1," + cw + "," + (x2 - dx) + "," + (y2 - dy) + "A" + r + "," + r + ",0,1," + cw + "," + (this._x1 = x0) + "," + (this._y1 = y0);
    } else if (da > epsilon) {
      this._ += "A" + r + "," + r + ",0," + +(da >= pi) + "," + cw + "," + (this._x1 = x2 + r * Math.cos(a1)) + "," + (this._y1 = y2 + r * Math.sin(a1));
    }
  },
  rect: function(x2, y2, w, h) {
    this._ += "M" + (this._x0 = this._x1 = +x2) + "," + (this._y0 = this._y1 = +y2) + "h" + +w + "v" + +h + "h" + -w + "Z";
  },
  toString: function() {
    return this._;
  }
};
var path_default2 = path;

// node_modules/react-d3-tree/node_modules/d3-shape/src/constant.js
function constant_default2(x2) {
  return function constant() {
    return x2;
  };
}

// node_modules/react-d3-tree/node_modules/d3-shape/src/math.js
var epsilon2 = 1e-12;
var pi2 = Math.PI;
var halfPi = pi2 / 2;
var tau2 = 2 * pi2;

// node_modules/react-d3-tree/node_modules/d3-shape/src/curve/linear.js
function Linear(context) {
  this._context = context;
}
Linear.prototype = {
  areaStart: function() {
    this._line = 0;
  },
  areaEnd: function() {
    this._line = NaN;
  },
  lineStart: function() {
    this._point = 0;
  },
  lineEnd: function() {
    if (this._line || this._line !== 0 && this._point === 1) this._context.closePath();
    this._line = 1 - this._line;
  },
  point: function(x2, y2) {
    x2 = +x2, y2 = +y2;
    switch (this._point) {
      case 0:
        this._point = 1;
        this._line ? this._context.lineTo(x2, y2) : this._context.moveTo(x2, y2);
        break;
      case 1:
        this._point = 2;
      // proceed
      default:
        this._context.lineTo(x2, y2);
        break;
    }
  }
};
function linear_default(context) {
  return new Linear(context);
}

// node_modules/react-d3-tree/node_modules/d3-shape/src/point.js
function x(p) {
  return p[0];
}
function y(p) {
  return p[1];
}

// node_modules/react-d3-tree/node_modules/d3-shape/src/curve/radial.js
var curveRadialLinear = curveRadial(linear_default);
function Radial(curve) {
  this._curve = curve;
}
Radial.prototype = {
  areaStart: function() {
    this._curve.areaStart();
  },
  areaEnd: function() {
    this._curve.areaEnd();
  },
  lineStart: function() {
    this._curve.lineStart();
  },
  lineEnd: function() {
    this._curve.lineEnd();
  },
  point: function(a2, r) {
    this._curve.point(r * Math.sin(a2), r * -Math.cos(a2));
  }
};
function curveRadial(curve) {
  function radial(context) {
    return new Radial(curve(context));
  }
  radial._curve = curve;
  return radial;
}

// node_modules/react-d3-tree/node_modules/d3-shape/src/array.js
var slice2 = Array.prototype.slice;

// node_modules/react-d3-tree/node_modules/d3-shape/src/link/index.js
function linkSource(d) {
  return d.source;
}
function linkTarget(d) {
  return d.target;
}
function link(curve) {
  var source = linkSource, target = linkTarget, x2 = x, y2 = y, context = null;
  function link2() {
    var buffer, argv = slice2.call(arguments), s2 = source.apply(this, argv), t = target.apply(this, argv);
    if (!context) context = buffer = path_default2();
    curve(context, +x2.apply(this, (argv[0] = s2, argv)), +y2.apply(this, argv), +x2.apply(this, (argv[0] = t, argv)), +y2.apply(this, argv));
    if (buffer) return context = null, buffer + "" || null;
  }
  link2.source = function(_) {
    return arguments.length ? (source = _, link2) : source;
  };
  link2.target = function(_) {
    return arguments.length ? (target = _, link2) : target;
  };
  link2.x = function(_) {
    return arguments.length ? (x2 = typeof _ === "function" ? _ : constant_default2(+_), link2) : x2;
  };
  link2.y = function(_) {
    return arguments.length ? (y2 = typeof _ === "function" ? _ : constant_default2(+_), link2) : y2;
  };
  link2.context = function(_) {
    return arguments.length ? (context = _ == null ? null : _, link2) : context;
  };
  return link2;
}
function curveHorizontal(context, x0, y0, x1, y1) {
  context.moveTo(x0, y0);
  context.bezierCurveTo(x0 = (x0 + x1) / 2, y0, x0, y1, x1, y1);
}
function curveVertical(context, x0, y0, x1, y1) {
  context.moveTo(x0, y0);
  context.bezierCurveTo(x0, y0 = (y0 + y1) / 2, x1, y0, x1, y1);
}
function linkHorizontal() {
  return link(curveHorizontal);
}
function linkVertical() {
  return link(curveVertical);
}

// node_modules/react-d3-tree/node_modules/d3-shape/src/symbol/diamond.js
var tan30 = Math.sqrt(1 / 3);
var tan30_2 = tan30 * 2;

// node_modules/react-d3-tree/node_modules/d3-shape/src/symbol/star.js
var kr = Math.sin(pi2 / 10) / Math.sin(7 * pi2 / 10);
var kx = Math.sin(tau2 / 10) * kr;
var ky = -Math.cos(tau2 / 10) * kr;

// node_modules/react-d3-tree/node_modules/d3-shape/src/symbol/triangle.js
var sqrt3 = Math.sqrt(3);

// node_modules/react-d3-tree/node_modules/d3-shape/src/symbol/wye.js
var s = Math.sqrt(3) / 2;
var k = 1 / Math.sqrt(12);
var a = (k / 2 + 1) * 3;

// node_modules/react-d3-tree/node_modules/d3-shape/src/noop.js
function noop_default() {
}

// node_modules/react-d3-tree/node_modules/d3-shape/src/curve/basis.js
function point(that, x2, y2) {
  that._context.bezierCurveTo(
    (2 * that._x0 + that._x1) / 3,
    (2 * that._y0 + that._y1) / 3,
    (that._x0 + 2 * that._x1) / 3,
    (that._y0 + 2 * that._y1) / 3,
    (that._x0 + 4 * that._x1 + x2) / 6,
    (that._y0 + 4 * that._y1 + y2) / 6
  );
}
function Basis(context) {
  this._context = context;
}
Basis.prototype = {
  areaStart: function() {
    this._line = 0;
  },
  areaEnd: function() {
    this._line = NaN;
  },
  lineStart: function() {
    this._x0 = this._x1 = this._y0 = this._y1 = NaN;
    this._point = 0;
  },
  lineEnd: function() {
    switch (this._point) {
      case 3:
        point(this, this._x1, this._y1);
      // proceed
      case 2:
        this._context.lineTo(this._x1, this._y1);
        break;
    }
    if (this._line || this._line !== 0 && this._point === 1) this._context.closePath();
    this._line = 1 - this._line;
  },
  point: function(x2, y2) {
    x2 = +x2, y2 = +y2;
    switch (this._point) {
      case 0:
        this._point = 1;
        this._line ? this._context.lineTo(x2, y2) : this._context.moveTo(x2, y2);
        break;
      case 1:
        this._point = 2;
        break;
      case 2:
        this._point = 3;
        this._context.lineTo((5 * this._x0 + this._x1) / 6, (5 * this._y0 + this._y1) / 6);
      // proceed
      default:
        point(this, x2, y2);
        break;
    }
    this._x0 = this._x1, this._x1 = x2;
    this._y0 = this._y1, this._y1 = y2;
  }
};

// node_modules/react-d3-tree/node_modules/d3-shape/src/curve/basisClosed.js
function BasisClosed(context) {
  this._context = context;
}
BasisClosed.prototype = {
  areaStart: noop_default,
  areaEnd: noop_default,
  lineStart: function() {
    this._x0 = this._x1 = this._x2 = this._x3 = this._x4 = this._y0 = this._y1 = this._y2 = this._y3 = this._y4 = NaN;
    this._point = 0;
  },
  lineEnd: function() {
    switch (this._point) {
      case 1: {
        this._context.moveTo(this._x2, this._y2);
        this._context.closePath();
        break;
      }
      case 2: {
        this._context.moveTo((this._x2 + 2 * this._x3) / 3, (this._y2 + 2 * this._y3) / 3);
        this._context.lineTo((this._x3 + 2 * this._x2) / 3, (this._y3 + 2 * this._y2) / 3);
        this._context.closePath();
        break;
      }
      case 3: {
        this.point(this._x2, this._y2);
        this.point(this._x3, this._y3);
        this.point(this._x4, this._y4);
        break;
      }
    }
  },
  point: function(x2, y2) {
    x2 = +x2, y2 = +y2;
    switch (this._point) {
      case 0:
        this._point = 1;
        this._x2 = x2, this._y2 = y2;
        break;
      case 1:
        this._point = 2;
        this._x3 = x2, this._y3 = y2;
        break;
      case 2:
        this._point = 3;
        this._x4 = x2, this._y4 = y2;
        this._context.moveTo((this._x0 + 4 * this._x1 + x2) / 6, (this._y0 + 4 * this._y1 + y2) / 6);
        break;
      default:
        point(this, x2, y2);
        break;
    }
    this._x0 = this._x1, this._x1 = x2;
    this._y0 = this._y1, this._y1 = y2;
  }
};

// node_modules/react-d3-tree/node_modules/d3-shape/src/curve/basisOpen.js
function BasisOpen(context) {
  this._context = context;
}
BasisOpen.prototype = {
  areaStart: function() {
    this._line = 0;
  },
  areaEnd: function() {
    this._line = NaN;
  },
  lineStart: function() {
    this._x0 = this._x1 = this._y0 = this._y1 = NaN;
    this._point = 0;
  },
  lineEnd: function() {
    if (this._line || this._line !== 0 && this._point === 3) this._context.closePath();
    this._line = 1 - this._line;
  },
  point: function(x2, y2) {
    x2 = +x2, y2 = +y2;
    switch (this._point) {
      case 0:
        this._point = 1;
        break;
      case 1:
        this._point = 2;
        break;
      case 2:
        this._point = 3;
        var x0 = (this._x0 + 4 * this._x1 + x2) / 6, y0 = (this._y0 + 4 * this._y1 + y2) / 6;
        this._line ? this._context.lineTo(x0, y0) : this._context.moveTo(x0, y0);
        break;
      case 3:
        this._point = 4;
      // proceed
      default:
        point(this, x2, y2);
        break;
    }
    this._x0 = this._x1, this._x1 = x2;
    this._y0 = this._y1, this._y1 = y2;
  }
};

// node_modules/react-d3-tree/node_modules/d3-shape/src/curve/bundle.js
function Bundle(context, beta) {
  this._basis = new Basis(context);
  this._beta = beta;
}
Bundle.prototype = {
  lineStart: function() {
    this._x = [];
    this._y = [];
    this._basis.lineStart();
  },
  lineEnd: function() {
    var x2 = this._x, y2 = this._y, j = x2.length - 1;
    if (j > 0) {
      var x0 = x2[0], y0 = y2[0], dx = x2[j] - x0, dy = y2[j] - y0, i = -1, t;
      while (++i <= j) {
        t = i / j;
        this._basis.point(
          this._beta * x2[i] + (1 - this._beta) * (x0 + t * dx),
          this._beta * y2[i] + (1 - this._beta) * (y0 + t * dy)
        );
      }
    }
    this._x = this._y = null;
    this._basis.lineEnd();
  },
  point: function(x2, y2) {
    this._x.push(+x2);
    this._y.push(+y2);
  }
};
var bundle_default = function custom3(beta) {
  function bundle(context) {
    return beta === 1 ? new Basis(context) : new Bundle(context, beta);
  }
  bundle.beta = function(beta2) {
    return custom3(+beta2);
  };
  return bundle;
}(0.85);

// node_modules/react-d3-tree/node_modules/d3-shape/src/curve/cardinal.js
function point2(that, x2, y2) {
  that._context.bezierCurveTo(
    that._x1 + that._k * (that._x2 - that._x0),
    that._y1 + that._k * (that._y2 - that._y0),
    that._x2 + that._k * (that._x1 - x2),
    that._y2 + that._k * (that._y1 - y2),
    that._x2,
    that._y2
  );
}
function Cardinal(context, tension) {
  this._context = context;
  this._k = (1 - tension) / 6;
}
Cardinal.prototype = {
  areaStart: function() {
    this._line = 0;
  },
  areaEnd: function() {
    this._line = NaN;
  },
  lineStart: function() {
    this._x0 = this._x1 = this._x2 = this._y0 = this._y1 = this._y2 = NaN;
    this._point = 0;
  },
  lineEnd: function() {
    switch (this._point) {
      case 2:
        this._context.lineTo(this._x2, this._y2);
        break;
      case 3:
        point2(this, this._x1, this._y1);
        break;
    }
    if (this._line || this._line !== 0 && this._point === 1) this._context.closePath();
    this._line = 1 - this._line;
  },
  point: function(x2, y2) {
    x2 = +x2, y2 = +y2;
    switch (this._point) {
      case 0:
        this._point = 1;
        this._line ? this._context.lineTo(x2, y2) : this._context.moveTo(x2, y2);
        break;
      case 1:
        this._point = 2;
        this._x1 = x2, this._y1 = y2;
        break;
      case 2:
        this._point = 3;
      // proceed
      default:
        point2(this, x2, y2);
        break;
    }
    this._x0 = this._x1, this._x1 = this._x2, this._x2 = x2;
    this._y0 = this._y1, this._y1 = this._y2, this._y2 = y2;
  }
};
var cardinal_default = function custom4(tension) {
  function cardinal(context) {
    return new Cardinal(context, tension);
  }
  cardinal.tension = function(tension2) {
    return custom4(+tension2);
  };
  return cardinal;
}(0);

// node_modules/react-d3-tree/node_modules/d3-shape/src/curve/cardinalClosed.js
function CardinalClosed(context, tension) {
  this._context = context;
  this._k = (1 - tension) / 6;
}
CardinalClosed.prototype = {
  areaStart: noop_default,
  areaEnd: noop_default,
  lineStart: function() {
    this._x0 = this._x1 = this._x2 = this._x3 = this._x4 = this._x5 = this._y0 = this._y1 = this._y2 = this._y3 = this._y4 = this._y5 = NaN;
    this._point = 0;
  },
  lineEnd: function() {
    switch (this._point) {
      case 1: {
        this._context.moveTo(this._x3, this._y3);
        this._context.closePath();
        break;
      }
      case 2: {
        this._context.lineTo(this._x3, this._y3);
        this._context.closePath();
        break;
      }
      case 3: {
        this.point(this._x3, this._y3);
        this.point(this._x4, this._y4);
        this.point(this._x5, this._y5);
        break;
      }
    }
  },
  point: function(x2, y2) {
    x2 = +x2, y2 = +y2;
    switch (this._point) {
      case 0:
        this._point = 1;
        this._x3 = x2, this._y3 = y2;
        break;
      case 1:
        this._point = 2;
        this._context.moveTo(this._x4 = x2, this._y4 = y2);
        break;
      case 2:
        this._point = 3;
        this._x5 = x2, this._y5 = y2;
        break;
      default:
        point2(this, x2, y2);
        break;
    }
    this._x0 = this._x1, this._x1 = this._x2, this._x2 = x2;
    this._y0 = this._y1, this._y1 = this._y2, this._y2 = y2;
  }
};
var cardinalClosed_default = function custom5(tension) {
  function cardinal(context) {
    return new CardinalClosed(context, tension);
  }
  cardinal.tension = function(tension2) {
    return custom5(+tension2);
  };
  return cardinal;
}(0);

// node_modules/react-d3-tree/node_modules/d3-shape/src/curve/cardinalOpen.js
function CardinalOpen(context, tension) {
  this._context = context;
  this._k = (1 - tension) / 6;
}
CardinalOpen.prototype = {
  areaStart: function() {
    this._line = 0;
  },
  areaEnd: function() {
    this._line = NaN;
  },
  lineStart: function() {
    this._x0 = this._x1 = this._x2 = this._y0 = this._y1 = this._y2 = NaN;
    this._point = 0;
  },
  lineEnd: function() {
    if (this._line || this._line !== 0 && this._point === 3) this._context.closePath();
    this._line = 1 - this._line;
  },
  point: function(x2, y2) {
    x2 = +x2, y2 = +y2;
    switch (this._point) {
      case 0:
        this._point = 1;
        break;
      case 1:
        this._point = 2;
        break;
      case 2:
        this._point = 3;
        this._line ? this._context.lineTo(this._x2, this._y2) : this._context.moveTo(this._x2, this._y2);
        break;
      case 3:
        this._point = 4;
      // proceed
      default:
        point2(this, x2, y2);
        break;
    }
    this._x0 = this._x1, this._x1 = this._x2, this._x2 = x2;
    this._y0 = this._y1, this._y1 = this._y2, this._y2 = y2;
  }
};
var cardinalOpen_default = function custom6(tension) {
  function cardinal(context) {
    return new CardinalOpen(context, tension);
  }
  cardinal.tension = function(tension2) {
    return custom6(+tension2);
  };
  return cardinal;
}(0);

// node_modules/react-d3-tree/node_modules/d3-shape/src/curve/catmullRom.js
function point3(that, x2, y2) {
  var x1 = that._x1, y1 = that._y1, x22 = that._x2, y22 = that._y2;
  if (that._l01_a > epsilon2) {
    var a2 = 2 * that._l01_2a + 3 * that._l01_a * that._l12_a + that._l12_2a, n = 3 * that._l01_a * (that._l01_a + that._l12_a);
    x1 = (x1 * a2 - that._x0 * that._l12_2a + that._x2 * that._l01_2a) / n;
    y1 = (y1 * a2 - that._y0 * that._l12_2a + that._y2 * that._l01_2a) / n;
  }
  if (that._l23_a > epsilon2) {
    var b = 2 * that._l23_2a + 3 * that._l23_a * that._l12_a + that._l12_2a, m = 3 * that._l23_a * (that._l23_a + that._l12_a);
    x22 = (x22 * b + that._x1 * that._l23_2a - x2 * that._l12_2a) / m;
    y22 = (y22 * b + that._y1 * that._l23_2a - y2 * that._l12_2a) / m;
  }
  that._context.bezierCurveTo(x1, y1, x22, y22, that._x2, that._y2);
}
function CatmullRom(context, alpha) {
  this._context = context;
  this._alpha = alpha;
}
CatmullRom.prototype = {
  areaStart: function() {
    this._line = 0;
  },
  areaEnd: function() {
    this._line = NaN;
  },
  lineStart: function() {
    this._x0 = this._x1 = this._x2 = this._y0 = this._y1 = this._y2 = NaN;
    this._l01_a = this._l12_a = this._l23_a = this._l01_2a = this._l12_2a = this._l23_2a = this._point = 0;
  },
  lineEnd: function() {
    switch (this._point) {
      case 2:
        this._context.lineTo(this._x2, this._y2);
        break;
      case 3:
        this.point(this._x2, this._y2);
        break;
    }
    if (this._line || this._line !== 0 && this._point === 1) this._context.closePath();
    this._line = 1 - this._line;
  },
  point: function(x2, y2) {
    x2 = +x2, y2 = +y2;
    if (this._point) {
      var x23 = this._x2 - x2, y23 = this._y2 - y2;
      this._l23_a = Math.sqrt(this._l23_2a = Math.pow(x23 * x23 + y23 * y23, this._alpha));
    }
    switch (this._point) {
      case 0:
        this._point = 1;
        this._line ? this._context.lineTo(x2, y2) : this._context.moveTo(x2, y2);
        break;
      case 1:
        this._point = 2;
        break;
      case 2:
        this._point = 3;
      // proceed
      default:
        point3(this, x2, y2);
        break;
    }
    this._l01_a = this._l12_a, this._l12_a = this._l23_a;
    this._l01_2a = this._l12_2a, this._l12_2a = this._l23_2a;
    this._x0 = this._x1, this._x1 = this._x2, this._x2 = x2;
    this._y0 = this._y1, this._y1 = this._y2, this._y2 = y2;
  }
};
var catmullRom_default = function custom7(alpha) {
  function catmullRom(context) {
    return alpha ? new CatmullRom(context, alpha) : new Cardinal(context, 0);
  }
  catmullRom.alpha = function(alpha2) {
    return custom7(+alpha2);
  };
  return catmullRom;
}(0.5);

// node_modules/react-d3-tree/node_modules/d3-shape/src/curve/catmullRomClosed.js
function CatmullRomClosed(context, alpha) {
  this._context = context;
  this._alpha = alpha;
}
CatmullRomClosed.prototype = {
  areaStart: noop_default,
  areaEnd: noop_default,
  lineStart: function() {
    this._x0 = this._x1 = this._x2 = this._x3 = this._x4 = this._x5 = this._y0 = this._y1 = this._y2 = this._y3 = this._y4 = this._y5 = NaN;
    this._l01_a = this._l12_a = this._l23_a = this._l01_2a = this._l12_2a = this._l23_2a = this._point = 0;
  },
  lineEnd: function() {
    switch (this._point) {
      case 1: {
        this._context.moveTo(this._x3, this._y3);
        this._context.closePath();
        break;
      }
      case 2: {
        this._context.lineTo(this._x3, this._y3);
        this._context.closePath();
        break;
      }
      case 3: {
        this.point(this._x3, this._y3);
        this.point(this._x4, this._y4);
        this.point(this._x5, this._y5);
        break;
      }
    }
  },
  point: function(x2, y2) {
    x2 = +x2, y2 = +y2;
    if (this._point) {
      var x23 = this._x2 - x2, y23 = this._y2 - y2;
      this._l23_a = Math.sqrt(this._l23_2a = Math.pow(x23 * x23 + y23 * y23, this._alpha));
    }
    switch (this._point) {
      case 0:
        this._point = 1;
        this._x3 = x2, this._y3 = y2;
        break;
      case 1:
        this._point = 2;
        this._context.moveTo(this._x4 = x2, this._y4 = y2);
        break;
      case 2:
        this._point = 3;
        this._x5 = x2, this._y5 = y2;
        break;
      default:
        point3(this, x2, y2);
        break;
    }
    this._l01_a = this._l12_a, this._l12_a = this._l23_a;
    this._l01_2a = this._l12_2a, this._l12_2a = this._l23_2a;
    this._x0 = this._x1, this._x1 = this._x2, this._x2 = x2;
    this._y0 = this._y1, this._y1 = this._y2, this._y2 = y2;
  }
};
var catmullRomClosed_default = function custom8(alpha) {
  function catmullRom(context) {
    return alpha ? new CatmullRomClosed(context, alpha) : new CardinalClosed(context, 0);
  }
  catmullRom.alpha = function(alpha2) {
    return custom8(+alpha2);
  };
  return catmullRom;
}(0.5);

// node_modules/react-d3-tree/node_modules/d3-shape/src/curve/catmullRomOpen.js
function CatmullRomOpen(context, alpha) {
  this._context = context;
  this._alpha = alpha;
}
CatmullRomOpen.prototype = {
  areaStart: function() {
    this._line = 0;
  },
  areaEnd: function() {
    this._line = NaN;
  },
  lineStart: function() {
    this._x0 = this._x1 = this._x2 = this._y0 = this._y1 = this._y2 = NaN;
    this._l01_a = this._l12_a = this._l23_a = this._l01_2a = this._l12_2a = this._l23_2a = this._point = 0;
  },
  lineEnd: function() {
    if (this._line || this._line !== 0 && this._point === 3) this._context.closePath();
    this._line = 1 - this._line;
  },
  point: function(x2, y2) {
    x2 = +x2, y2 = +y2;
    if (this._point) {
      var x23 = this._x2 - x2, y23 = this._y2 - y2;
      this._l23_a = Math.sqrt(this._l23_2a = Math.pow(x23 * x23 + y23 * y23, this._alpha));
    }
    switch (this._point) {
      case 0:
        this._point = 1;
        break;
      case 1:
        this._point = 2;
        break;
      case 2:
        this._point = 3;
        this._line ? this._context.lineTo(this._x2, this._y2) : this._context.moveTo(this._x2, this._y2);
        break;
      case 3:
        this._point = 4;
      // proceed
      default:
        point3(this, x2, y2);
        break;
    }
    this._l01_a = this._l12_a, this._l12_a = this._l23_a;
    this._l01_2a = this._l12_2a, this._l12_2a = this._l23_2a;
    this._x0 = this._x1, this._x1 = this._x2, this._x2 = x2;
    this._y0 = this._y1, this._y1 = this._y2, this._y2 = y2;
  }
};
var catmullRomOpen_default = function custom9(alpha) {
  function catmullRom(context) {
    return alpha ? new CatmullRomOpen(context, alpha) : new CardinalOpen(context, 0);
  }
  catmullRom.alpha = function(alpha2) {
    return custom9(+alpha2);
  };
  return catmullRom;
}(0.5);

// node_modules/react-d3-tree/node_modules/d3-shape/src/curve/linearClosed.js
function LinearClosed(context) {
  this._context = context;
}
LinearClosed.prototype = {
  areaStart: noop_default,
  areaEnd: noop_default,
  lineStart: function() {
    this._point = 0;
  },
  lineEnd: function() {
    if (this._point) this._context.closePath();
  },
  point: function(x2, y2) {
    x2 = +x2, y2 = +y2;
    if (this._point) this._context.lineTo(x2, y2);
    else this._point = 1, this._context.moveTo(x2, y2);
  }
};

// node_modules/react-d3-tree/node_modules/d3-shape/src/curve/monotone.js
function sign(x2) {
  return x2 < 0 ? -1 : 1;
}
function slope3(that, x2, y2) {
  var h0 = that._x1 - that._x0, h1 = x2 - that._x1, s0 = (that._y1 - that._y0) / (h0 || h1 < 0 && -0), s1 = (y2 - that._y1) / (h1 || h0 < 0 && -0), p = (s0 * h1 + s1 * h0) / (h0 + h1);
  return (sign(s0) + sign(s1)) * Math.min(Math.abs(s0), Math.abs(s1), 0.5 * Math.abs(p)) || 0;
}
function slope2(that, t) {
  var h = that._x1 - that._x0;
  return h ? (3 * (that._y1 - that._y0) / h - t) / 2 : t;
}
function point4(that, t0, t1) {
  var x0 = that._x0, y0 = that._y0, x1 = that._x1, y1 = that._y1, dx = (x1 - x0) / 3;
  that._context.bezierCurveTo(x0 + dx, y0 + dx * t0, x1 - dx, y1 - dx * t1, x1, y1);
}
function MonotoneX(context) {
  this._context = context;
}
MonotoneX.prototype = {
  areaStart: function() {
    this._line = 0;
  },
  areaEnd: function() {
    this._line = NaN;
  },
  lineStart: function() {
    this._x0 = this._x1 = this._y0 = this._y1 = this._t0 = NaN;
    this._point = 0;
  },
  lineEnd: function() {
    switch (this._point) {
      case 2:
        this._context.lineTo(this._x1, this._y1);
        break;
      case 3:
        point4(this, this._t0, slope2(this, this._t0));
        break;
    }
    if (this._line || this._line !== 0 && this._point === 1) this._context.closePath();
    this._line = 1 - this._line;
  },
  point: function(x2, y2) {
    var t1 = NaN;
    x2 = +x2, y2 = +y2;
    if (x2 === this._x1 && y2 === this._y1) return;
    switch (this._point) {
      case 0:
        this._point = 1;
        this._line ? this._context.lineTo(x2, y2) : this._context.moveTo(x2, y2);
        break;
      case 1:
        this._point = 2;
        break;
      case 2:
        this._point = 3;
        point4(this, slope2(this, t1 = slope3(this, x2, y2)), t1);
        break;
      default:
        point4(this, this._t0, t1 = slope3(this, x2, y2));
        break;
    }
    this._x0 = this._x1, this._x1 = x2;
    this._y0 = this._y1, this._y1 = y2;
    this._t0 = t1;
  }
};
function MonotoneY(context) {
  this._context = new ReflectContext(context);
}
(MonotoneY.prototype = Object.create(MonotoneX.prototype)).point = function(x2, y2) {
  MonotoneX.prototype.point.call(this, y2, x2);
};
function ReflectContext(context) {
  this._context = context;
}
ReflectContext.prototype = {
  moveTo: function(x2, y2) {
    this._context.moveTo(y2, x2);
  },
  closePath: function() {
    this._context.closePath();
  },
  lineTo: function(x2, y2) {
    this._context.lineTo(y2, x2);
  },
  bezierCurveTo: function(x1, y1, x2, y2, x3, y3) {
    this._context.bezierCurveTo(y1, x1, y2, x2, y3, x3);
  }
};

// node_modules/react-d3-tree/node_modules/d3-shape/src/curve/natural.js
function Natural(context) {
  this._context = context;
}
Natural.prototype = {
  areaStart: function() {
    this._line = 0;
  },
  areaEnd: function() {
    this._line = NaN;
  },
  lineStart: function() {
    this._x = [];
    this._y = [];
  },
  lineEnd: function() {
    var x2 = this._x, y2 = this._y, n = x2.length;
    if (n) {
      this._line ? this._context.lineTo(x2[0], y2[0]) : this._context.moveTo(x2[0], y2[0]);
      if (n === 2) {
        this._context.lineTo(x2[1], y2[1]);
      } else {
        var px = controlPoints(x2), py = controlPoints(y2);
        for (var i0 = 0, i1 = 1; i1 < n; ++i0, ++i1) {
          this._context.bezierCurveTo(px[0][i0], py[0][i0], px[1][i0], py[1][i0], x2[i1], y2[i1]);
        }
      }
    }
    if (this._line || this._line !== 0 && n === 1) this._context.closePath();
    this._line = 1 - this._line;
    this._x = this._y = null;
  },
  point: function(x2, y2) {
    this._x.push(+x2);
    this._y.push(+y2);
  }
};
function controlPoints(x2) {
  var i, n = x2.length - 1, m, a2 = new Array(n), b = new Array(n), r = new Array(n);
  a2[0] = 0, b[0] = 2, r[0] = x2[0] + 2 * x2[1];
  for (i = 1; i < n - 1; ++i) a2[i] = 1, b[i] = 4, r[i] = 4 * x2[i] + 2 * x2[i + 1];
  a2[n - 1] = 2, b[n - 1] = 7, r[n - 1] = 8 * x2[n - 1] + x2[n];
  for (i = 1; i < n; ++i) m = a2[i] / b[i - 1], b[i] -= m, r[i] -= m * r[i - 1];
  a2[n - 1] = r[n - 1] / b[n - 1];
  for (i = n - 2; i >= 0; --i) a2[i] = (r[i] - a2[i + 1]) / b[i];
  b[n - 1] = (x2[n] + a2[n - 1]) / 2;
  for (i = 0; i < n - 1; ++i) b[i] = 2 * x2[i + 1] - a2[i + 1];
  return [a2, b];
}

// node_modules/react-d3-tree/node_modules/d3-shape/src/curve/step.js
function Step(context, t) {
  this._context = context;
  this._t = t;
}
Step.prototype = {
  areaStart: function() {
    this._line = 0;
  },
  areaEnd: function() {
    this._line = NaN;
  },
  lineStart: function() {
    this._x = this._y = NaN;
    this._point = 0;
  },
  lineEnd: function() {
    if (0 < this._t && this._t < 1 && this._point === 2) this._context.lineTo(this._x, this._y);
    if (this._line || this._line !== 0 && this._point === 1) this._context.closePath();
    if (this._line >= 0) this._t = 1 - this._t, this._line = 1 - this._line;
  },
  point: function(x2, y2) {
    x2 = +x2, y2 = +y2;
    switch (this._point) {
      case 0:
        this._point = 1;
        this._line ? this._context.lineTo(x2, y2) : this._context.moveTo(x2, y2);
        break;
      case 1:
        this._point = 2;
      // proceed
      default: {
        if (this._t <= 0) {
          this._context.lineTo(this._x, y2);
          this._context.lineTo(x2, y2);
        } else {
          var x1 = this._x * (1 - this._t) + x2 * this._t;
          this._context.lineTo(x1, this._y);
          this._context.lineTo(x1, y2);
        }
        break;
      }
    }
    this._x = x2, this._y = y2;
  }
};

// node_modules/react-d3-tree/lib/esm/Link/index.js
var Link = class extends import_react4.default.PureComponent {
  constructor() {
    super(...arguments);
    this.linkRef = null;
    this.state = {
      initialStyle: {
        opacity: 0
      }
    };
    this.handleOnClick = (evt) => {
      this.props.onClick(this.props.linkData.source, this.props.linkData.target, evt);
    };
    this.handleOnMouseOver = (evt) => {
      this.props.onMouseOver(this.props.linkData.source, this.props.linkData.target, evt);
    };
    this.handleOnMouseOut = (evt) => {
      this.props.onMouseOut(this.props.linkData.source, this.props.linkData.target, evt);
    };
  }
  componentDidMount() {
    this.applyOpacity(1, this.props.transitionDuration);
  }
  componentWillLeave(done) {
    this.applyOpacity(0, this.props.transitionDuration, done);
  }
  applyOpacity(opacity, transitionDuration, done = () => {
  }) {
    if (this.props.enableLegacyTransitions) {
      select_default(this.linkRef).transition().duration(transitionDuration).style("opacity", opacity).on("end", done);
    } else {
      select_default(this.linkRef).style("opacity", opacity);
      done();
    }
  }
  drawStepPath(linkData, orientation) {
    const { source, target } = linkData;
    const deltaY = target.y - source.y;
    return orientation === "horizontal" ? `M${source.y},${source.x} H${source.y + deltaY / 2} V${target.x} H${target.y}` : `M${source.x},${source.y} V${source.y + deltaY / 2} H${target.x} V${target.y}`;
  }
  drawDiagonalPath(linkData, orientation) {
    const { source, target } = linkData;
    return orientation === "horizontal" ? linkHorizontal()({
      source: [source.y, source.x],
      target: [target.y, target.x]
    }) : linkVertical()({
      source: [source.x, source.y],
      target: [target.x, target.y]
    });
  }
  drawStraightPath(linkData, orientation) {
    const { source, target } = linkData;
    return orientation === "horizontal" ? `M${source.y},${source.x}L${target.y},${target.x}` : `M${source.x},${source.y}L${target.x},${target.y}`;
  }
  drawElbowPath(linkData, orientation) {
    return orientation === "horizontal" ? `M${linkData.source.y},${linkData.source.x}V${linkData.target.x}H${linkData.target.y}` : `M${linkData.source.x},${linkData.source.y}V${linkData.target.y}H${linkData.target.x}`;
  }
  drawPath() {
    const { linkData, orientation, pathFunc } = this.props;
    if (typeof pathFunc === "function") {
      return pathFunc(linkData, orientation);
    }
    if (pathFunc === "elbow") {
      return this.drawElbowPath(linkData, orientation);
    }
    if (pathFunc === "straight") {
      return this.drawStraightPath(linkData, orientation);
    }
    if (pathFunc === "step") {
      return this.drawStepPath(linkData, orientation);
    }
    return this.drawDiagonalPath(linkData, orientation);
  }
  getClassNames() {
    const { linkData, orientation, pathClassFunc } = this.props;
    const classNames = ["rd3t-link"];
    if (typeof pathClassFunc === "function") {
      classNames.push(pathClassFunc(linkData, orientation));
    }
    return classNames.join(" ").trim();
  }
  render() {
    const { linkData } = this.props;
    return import_react4.default.createElement("path", { ref: (l) => {
      this.linkRef = l;
    }, style: Object.assign({}, this.state.initialStyle), className: this.getClassNames(), d: this.drawPath(), onClick: this.handleOnClick, onMouseOver: this.handleOnMouseOver, onMouseOut: this.handleOnMouseOut, "data-source-id": linkData.source.id, "data-target-id": linkData.target.id });
  }
};

// node_modules/react-d3-tree/lib/esm/globalCss.js
var globalCss_default = `
/* Tree */
.rd3t-tree-container {
  width: 100%;
  height: 100%;
}

.rd3t-grabbable {
  cursor: move; /* fallback if grab cursor is unsupported */
  cursor: grab;
  cursor: -moz-grab;
  cursor: -webkit-grab;
}
.rd3t-grabbable:active {
    cursor: grabbing;
    cursor: -moz-grabbing;
    cursor: -webkit-grabbing;
}

/* Node */
.rd3t-node {
  cursor: pointer;
  fill: #777;
  stroke: #000;
  stroke-width: 2;
}

.rd3t-leaf-node {
  cursor: pointer;
  fill: transparent;
  stroke: #000;
  stroke-width: 1;
}

.rd3t-label__title {
  fill: #000;
  stroke: none;
  font-weight: bolder;
}

.rd3t-label__attributes {
  fill: #777;
  stroke: none;
  font-weight: bolder;
  font-size: smaller;
}

/* Link */
.rd3t-link {
  fill: none;
  stroke: #000;
}
`;

// node_modules/react-d3-tree/lib/esm/Tree/index.js
var Tree = class _Tree extends import_react5.default.Component {
  constructor() {
    super(...arguments);
    this.state = {
      dataRef: this.props.data,
      data: _Tree.assignInternalProperties((0, import_clone.default)(this.props.data)),
      d3: _Tree.calculateD3Geometry(this.props),
      isTransitioning: false,
      isInitialRenderForDataset: true,
      dataKey: this.props.dataKey
    };
    this.internalState = {
      targetNode: null,
      isTransitioning: false
    };
    this.svgInstanceRef = `rd3t-svg-${v4_default()}`;
    this.gInstanceRef = `rd3t-g-${v4_default()}`;
    this.handleNodeToggle = (nodeId) => {
      const data = (0, import_clone.default)(this.state.data);
      const matches = this.findNodesById(nodeId, data, []);
      const targetNodeDatum = matches[0];
      if (this.props.collapsible && !this.state.isTransitioning) {
        if (targetNodeDatum.__rd3t.collapsed) {
          _Tree.expandNode(targetNodeDatum);
          this.props.shouldCollapseNeighborNodes && this.collapseNeighborNodes(targetNodeDatum, data);
        } else {
          _Tree.collapseNode(targetNodeDatum);
        }
        if (this.props.enableLegacyTransitions) {
          this.setState({ data, isTransitioning: true });
          setTimeout(() => this.setState({ isTransitioning: false }), this.props.transitionDuration + 10);
        } else {
          this.setState({ data });
        }
        this.internalState.targetNode = targetNodeDatum;
      }
    };
    this.handleAddChildrenToNode = (nodeId, childrenData) => {
      const data = (0, import_clone.default)(this.state.data);
      const matches = this.findNodesById(nodeId, data, []);
      if (matches.length > 0) {
        const targetNodeDatum = matches[0];
        const depth = targetNodeDatum.__rd3t.depth;
        const formattedChildren = (0, import_clone.default)(childrenData).map((node) => _Tree.assignInternalProperties([node], depth + 1));
        targetNodeDatum.children.push(...formattedChildren.flat());
        this.setState({ data });
      }
    };
    this.handleOnNodeClickCb = (hierarchyPointNode, evt) => {
      const { onNodeClick } = this.props;
      if (onNodeClick && typeof onNodeClick === "function") {
        evt.persist();
        onNodeClick((0, import_clone.default)(hierarchyPointNode), evt);
      }
    };
    this.handleOnLinkClickCb = (linkSource2, linkTarget2, evt) => {
      const { onLinkClick } = this.props;
      if (onLinkClick && typeof onLinkClick === "function") {
        evt.persist();
        onLinkClick((0, import_clone.default)(linkSource2), (0, import_clone.default)(linkTarget2), evt);
      }
    };
    this.handleOnNodeMouseOverCb = (hierarchyPointNode, evt) => {
      const { onNodeMouseOver } = this.props;
      if (onNodeMouseOver && typeof onNodeMouseOver === "function") {
        evt.persist();
        onNodeMouseOver((0, import_clone.default)(hierarchyPointNode), evt);
      }
    };
    this.handleOnLinkMouseOverCb = (linkSource2, linkTarget2, evt) => {
      const { onLinkMouseOver } = this.props;
      if (onLinkMouseOver && typeof onLinkMouseOver === "function") {
        evt.persist();
        onLinkMouseOver((0, import_clone.default)(linkSource2), (0, import_clone.default)(linkTarget2), evt);
      }
    };
    this.handleOnNodeMouseOutCb = (hierarchyPointNode, evt) => {
      const { onNodeMouseOut } = this.props;
      if (onNodeMouseOut && typeof onNodeMouseOut === "function") {
        evt.persist();
        onNodeMouseOut((0, import_clone.default)(hierarchyPointNode), evt);
      }
    };
    this.handleOnLinkMouseOutCb = (linkSource2, linkTarget2, evt) => {
      const { onLinkMouseOut } = this.props;
      if (onLinkMouseOut && typeof onLinkMouseOut === "function") {
        evt.persist();
        onLinkMouseOut((0, import_clone.default)(linkSource2), (0, import_clone.default)(linkTarget2), evt);
      }
    };
    this.centerNode = (hierarchyPointNode) => {
      const { dimensions, orientation, zoom, centeringTransitionDuration } = this.props;
      if (dimensions) {
        const g = select_default(`.${this.gInstanceRef}`);
        const svg = select_default(`.${this.svgInstanceRef}`);
        const scale = this.state.d3.scale;
        let x2;
        let y2;
        if (orientation === "horizontal") {
          y2 = -hierarchyPointNode.x * scale + dimensions.height / 2;
          x2 = -hierarchyPointNode.y * scale + dimensions.width / 2;
        } else {
          x2 = -hierarchyPointNode.x * scale + dimensions.width / 2;
          y2 = -hierarchyPointNode.y * scale + dimensions.height / 2;
        }
        g.transition().duration(centeringTransitionDuration).attr("transform", "translate(" + x2 + "," + y2 + ")scale(" + scale + ")");
        svg.call(zoom_default().transform, identity.translate(x2, y2).scale(zoom));
      }
    };
    this.getNodeClassName = (parent, nodeDatum) => {
      const { rootNodeClassName, branchNodeClassName, leafNodeClassName } = this.props;
      const hasParent = parent !== null && parent !== void 0;
      if (hasParent) {
        return nodeDatum.children ? branchNodeClassName : leafNodeClassName;
      } else {
        return rootNodeClassName;
      }
    };
  }
  static getDerivedStateFromProps(nextProps, prevState) {
    let derivedState = null;
    const dataKeyChanged = !nextProps.dataKey || prevState.dataKey !== nextProps.dataKey;
    if (nextProps.data !== prevState.dataRef && dataKeyChanged) {
      derivedState = {
        dataRef: nextProps.data,
        data: _Tree.assignInternalProperties((0, import_clone.default)(nextProps.data)),
        isInitialRenderForDataset: true,
        dataKey: nextProps.dataKey
      };
    }
    const d3 = _Tree.calculateD3Geometry(nextProps);
    if (!dequal(d3, prevState.d3)) {
      derivedState = derivedState || {};
      derivedState.d3 = d3;
    }
    return derivedState;
  }
  componentDidMount() {
    this.bindZoomListener(this.props);
    this.setState({ isInitialRenderForDataset: false });
  }
  componentDidUpdate(prevProps) {
    if (this.props.data !== prevProps.data) {
      this.setState({ isInitialRenderForDataset: false });
    }
    if (!dequal(this.props.translate, prevProps.translate) || !dequal(this.props.scaleExtent, prevProps.scaleExtent) || this.props.zoomable !== prevProps.zoomable || this.props.draggable !== prevProps.draggable || this.props.zoom !== prevProps.zoom || this.props.enableLegacyTransitions !== prevProps.enableLegacyTransitions) {
      this.bindZoomListener(this.props);
    }
    if (typeof this.props.onUpdate === "function") {
      this.props.onUpdate({
        node: this.internalState.targetNode ? (0, import_clone.default)(this.internalState.targetNode) : null,
        zoom: this.state.d3.scale,
        translate: this.state.d3.translate
      });
    }
    this.internalState.targetNode = null;
  }
  /**
   * Collapses all tree nodes with a `depth` larger than `initialDepth`.
   *
   * @param {array} nodeSet Array of nodes generated by `generateTree`
   * @param {number} initialDepth Maximum initial depth the tree should render
   */
  setInitialTreeDepth(nodeSet, initialDepth) {
    nodeSet.forEach((n) => {
      n.data.__rd3t.collapsed = n.depth >= initialDepth;
    });
  }
  /**
   * bindZoomListener - If `props.zoomable`, binds a listener for
   * "zoom" events to the SVG and sets scaleExtent to min/max
   * specified in `props.scaleExtent`.
   */
  bindZoomListener(props) {
    const { zoomable, scaleExtent, translate, zoom, onUpdate, hasInteractiveNodes } = props;
    const svg = select_default(`.${this.svgInstanceRef}`);
    const g = select_default(`.${this.gInstanceRef}`);
    svg.call(zoom_default().transform, identity.translate(translate.x, translate.y).scale(zoom));
    svg.call(zoom_default().scaleExtent(zoomable ? [scaleExtent.min, scaleExtent.max] : [zoom, zoom]).filter((event) => {
      if (hasInteractiveNodes) {
        return event.target.classList.contains(this.svgInstanceRef) || event.target.classList.contains(this.gInstanceRef) || event.shiftKey;
      }
      return true;
    }).on("zoom", (event) => {
      if (!this.props.draggable && ["mousemove", "touchmove", "dblclick"].includes(event.sourceEvent.type)) {
        return;
      }
      g.attr("transform", event.transform);
      if (typeof onUpdate === "function") {
        onUpdate({
          node: null,
          zoom: event.transform.k,
          translate: { x: event.transform.x, y: event.transform.y }
        });
        this.state.d3.scale = event.transform.k;
        this.state.d3.translate = {
          x: event.transform.x,
          y: event.transform.y
        };
      }
    }));
  }
  /**
   * Assigns internal properties that are required for tree
   * manipulation to each node in the `data` set and returns a new `data` array.
   *
   * @static
   */
  static assignInternalProperties(data, currentDepth = 0) {
    const d = Array.isArray(data) ? data : [data];
    return d.map((n) => {
      const nodeDatum = n;
      nodeDatum.__rd3t = { id: null, depth: null, collapsed: false };
      nodeDatum.__rd3t.id = v4_default();
      nodeDatum.__rd3t.depth = currentDepth;
      if (nodeDatum.children && nodeDatum.children.length > 0) {
        nodeDatum.children = _Tree.assignInternalProperties(nodeDatum.children, currentDepth + 1);
      }
      return nodeDatum;
    });
  }
  /**
   * Recursively walks the nested `nodeSet` until a node matching `nodeId` is found.
   */
  findNodesById(nodeId, nodeSet, hits) {
    if (hits.length > 0) {
      return hits;
    }
    hits = hits.concat(nodeSet.filter((node) => node.__rd3t.id === nodeId));
    nodeSet.forEach((node) => {
      if (node.children && node.children.length > 0) {
        hits = this.findNodesById(nodeId, node.children, hits);
      }
    });
    return hits;
  }
  /**
   * Recursively walks the nested `nodeSet` until all nodes at `depth` have been found.
   *
   * @param {number} depth Target depth for which nodes should be returned
   * @param {array} nodeSet Array of nested `node` objects
   * @param {array} accumulator Accumulator for matches, passed between recursive calls
   */
  findNodesAtDepth(depth, nodeSet, accumulator) {
    accumulator = accumulator.concat(nodeSet.filter((node) => node.__rd3t.depth === depth));
    nodeSet.forEach((node) => {
      if (node.children && node.children.length > 0) {
        accumulator = this.findNodesAtDepth(depth, node.children, accumulator);
      }
    });
    return accumulator;
  }
  /**
   * Recursively sets the internal `collapsed` property of
   * the passed `TreeNodeDatum` and its children to `true`.
   *
   * @static
   */
  static collapseNode(nodeDatum) {
    nodeDatum.__rd3t.collapsed = true;
    if (nodeDatum.children && nodeDatum.children.length > 0) {
      nodeDatum.children.forEach((child) => {
        _Tree.collapseNode(child);
      });
    }
  }
  /**
   * Sets the internal `collapsed` property of
   * the passed `TreeNodeDatum` object to `false`.
   *
   * @static
   */
  static expandNode(nodeDatum) {
    nodeDatum.__rd3t.collapsed = false;
  }
  /**
   * Collapses all nodes in `nodeSet` that are neighbors (same depth) of `targetNode`.
   */
  collapseNeighborNodes(targetNode, nodeSet) {
    const neighbors = this.findNodesAtDepth(targetNode.__rd3t.depth, nodeSet, []).filter((node) => node.__rd3t.id !== targetNode.__rd3t.id);
    neighbors.forEach((neighbor) => _Tree.collapseNode(neighbor));
  }
  /**
   * Generates tree elements (`nodes` and `links`) by
   * grabbing the rootNode from `this.state.data[0]`.
   * Restricts tree depth to `props.initialDepth` if defined and if this is
   * the initial render of the tree.
   */
  generateTree() {
    const { initialDepth, depthFactor, separation, nodeSize, orientation } = this.props;
    const { isInitialRenderForDataset } = this.state;
    const tree = tree_default().nodeSize(orientation === "horizontal" ? [nodeSize.y, nodeSize.x] : [nodeSize.x, nodeSize.y]).separation((a2, b) => a2.parent.data.__rd3t.id === b.parent.data.__rd3t.id ? separation.siblings : separation.nonSiblings);
    const rootNode = tree(hierarchy(this.state.data[0], (d) => d.__rd3t.collapsed ? null : d.children));
    let nodes = rootNode.descendants();
    const links = rootNode.links();
    if (initialDepth !== void 0 && isInitialRenderForDataset) {
      this.setInitialTreeDepth(nodes, initialDepth);
    }
    if (depthFactor) {
      nodes.forEach((node) => {
        node.y = node.depth * depthFactor;
      });
    }
    return { nodes, links };
  }
  /**
   * Set initial zoom and position.
   * Also limit zoom level according to `scaleExtent` on initial display. This is necessary,
   * because the first time we are setting it as an SVG property, instead of going
   * through D3's scaling mechanism, which would have picked up both properties.
   *
   * @static
   */
  static calculateD3Geometry(nextProps) {
    let scale;
    if (nextProps.zoom > nextProps.scaleExtent.max) {
      scale = nextProps.scaleExtent.max;
    } else if (nextProps.zoom < nextProps.scaleExtent.min) {
      scale = nextProps.scaleExtent.min;
    } else {
      scale = nextProps.zoom;
    }
    return {
      translate: nextProps.translate,
      scale
    };
  }
  render() {
    const { nodes, links } = this.generateTree();
    const { renderCustomNodeElement, orientation, pathFunc, transitionDuration, nodeSize, depthFactor, initialDepth, separation, enableLegacyTransitions, svgClassName, pathClassFunc } = this.props;
    const { translate, scale } = this.state.d3;
    const subscriptions = Object.assign(Object.assign(Object.assign({}, nodeSize), separation), {
      depthFactor,
      initialDepth
    });
    return import_react5.default.createElement(
      "div",
      { className: "rd3t-tree-container rd3t-grabbable" },
      import_react5.default.createElement("style", null, globalCss_default),
      import_react5.default.createElement(
        "svg",
        { className: `rd3t-svg ${this.svgInstanceRef} ${svgClassName}`, width: "100%", height: "100%" },
        import_react5.default.createElement(
          TransitionGroupWrapper_default,
          { enableLegacyTransitions, component: "g", className: `rd3t-g ${this.gInstanceRef}`, transform: `translate(${translate.x},${translate.y}) scale(${scale})` },
          links.map((linkData, i) => {
            return import_react5.default.createElement(Link, { key: "link-" + i, orientation, pathFunc, pathClassFunc, linkData, onClick: this.handleOnLinkClickCb, onMouseOver: this.handleOnLinkMouseOverCb, onMouseOut: this.handleOnLinkMouseOutCb, enableLegacyTransitions, transitionDuration });
          }),
          nodes.map((hierarchyPointNode, i) => {
            const { data, x: x2, y: y2, parent } = hierarchyPointNode;
            return import_react5.default.createElement(Node2, { key: "node-" + i, data, position: { x: x2, y: y2 }, hierarchyPointNode, parent, nodeClassName: this.getNodeClassName(parent, data), renderCustomNodeElement, nodeSize, orientation, enableLegacyTransitions, transitionDuration, onNodeToggle: this.handleNodeToggle, onNodeClick: this.handleOnNodeClickCb, onNodeMouseOver: this.handleOnNodeMouseOverCb, onNodeMouseOut: this.handleOnNodeMouseOutCb, handleAddChildrenToNode: this.handleAddChildrenToNode, subscriptions, centerNode: this.centerNode });
          })
        )
      )
    );
  }
};
Tree.defaultProps = {
  onNodeClick: void 0,
  onNodeMouseOver: void 0,
  onNodeMouseOut: void 0,
  onLinkClick: void 0,
  onLinkMouseOver: void 0,
  onLinkMouseOut: void 0,
  onUpdate: void 0,
  orientation: "horizontal",
  translate: { x: 0, y: 0 },
  pathFunc: "diagonal",
  pathClassFunc: void 0,
  transitionDuration: 500,
  depthFactor: void 0,
  collapsible: true,
  initialDepth: void 0,
  zoomable: true,
  draggable: true,
  zoom: 1,
  scaleExtent: { min: 0.1, max: 1 },
  nodeSize: { x: 140, y: 140 },
  separation: { siblings: 1, nonSiblings: 2 },
  shouldCollapseNeighborNodes: false,
  svgClassName: "",
  rootNodeClassName: "",
  branchNodeClassName: "",
  leafNodeClassName: "",
  renderCustomNodeElement: void 0,
  enableLegacyTransitions: false,
  hasInteractiveNodes: false,
  dimensions: void 0,
  centeringTransitionDuration: 800,
  dataKey: void 0
};
var Tree_default = Tree;

// node_modules/react-d3-tree/lib/esm/index.js
var esm_default = Tree_default;
export {
  Tree_default as Tree,
  esm_default as default
};
//# sourceMappingURL=react-d3-tree.js.map
